# 🚀 Facebook Automation Desktop - Scripts Guide

Hướng dẫn đầy đủ về bộ scripts tự động hóa cho Facebook Automation Desktop Application.

## 📋 Tổng quan

Bộ scripts này cung cấp giải pháp hoàn chỉnh cho:
- ✅ Setup môi trường development và production
- ✅ Build application cho nhiều target khác nhau
- ✅ Run application locally với monitoring
- ✅ Deploy tự động với nhiều phương thức
- ✅ Quản lý unified qua một interface duy nhất

## 🎯 Quick Start

### Bước 1: Make Scripts Executable
```bash
./scripts/make-executable.sh
```

### Bước 2: Setup Development Environment
```bash
# Linux/macOS
./scripts/setup-local.sh

# Windows
scripts\setup-local.bat
```

### Bước 3: Run Application
```bash
# Quick start
./scripts/dev.sh

# Full featured
./scripts/run-local.sh

# Unified management
./scripts/manage.sh run
```

## 📁 Scripts Overview

### 🔧 Setup Scripts
| Script | Platform | Purpose |
|--------|----------|---------|
| `setup-local.sh` | Linux/macOS | Setup development environment |
| `setup-local.bat` | Windows | Setup development environment |
| `setup-production.sh` | Linux | Setup production server |

### 🏗️ Build Scripts
| Script | Platform | Purpose |
|--------|----------|---------|
| `build-local.sh` | Linux/macOS | Build for development |
| `build-local.bat` | Windows | Build for development |
| `build-production.sh` | Linux/macOS | Build for production |

### 🚀 Run Scripts
| Script | Platform | Purpose |
|--------|----------|---------|
| `run-local.sh` | Linux/macOS | Run application locally (full featured) |
| `run-local.bat` | Windows | Run application locally (full featured) |
| `dev.sh` | Linux/macOS | Quick development start |
| `dev.bat` | Windows | Quick development start |

### 📦 Deploy Scripts
| Script | Platform | Purpose |
|--------|----------|---------|
| `deploy.sh` | Linux/macOS | Automated deployment |

### 🎯 Management Scripts
| Script | Platform | Purpose |
|--------|----------|---------|
| `manage.sh` | Linux/macOS | Unified management interface |
| `make-executable.sh` | Linux/macOS | Make all scripts executable |
| `test-scripts.sh` | Linux/macOS | Test all scripts |
| `demo.sh` | Linux/macOS | Interactive demo |

## 🔄 Typical Workflows

### Development Workflow
```bash
# 1. Initial setup (one time)
./scripts/make-executable.sh
./scripts/setup-local.sh

# 2. Daily development
./scripts/dev.sh                    # Start development
# ... do your work ...
Ctrl+C                              # Stop when done

# 3. Testing
./scripts/manage.sh test all        # Run tests
./scripts/build-local.sh            # Test build
```

### Production Deployment Workflow
```bash
# 1. Build for production
./scripts/build-production.sh

# 2. Deploy
./scripts/deploy.sh local traditional           # Local deployment
./scripts/deploy.sh remote docker -h server.com # Remote deployment

# 3. Monitor
./scripts/manage.sh status          # Check status
./scripts/manage.sh logs            # View logs
```

## 🎮 Command Examples

### Setup Commands
```bash
# Development setup
./scripts/setup-local.sh

# Production setup with options
sudo ./scripts/setup-production.sh --skip-deps --skip-db
```

### Run Commands
```bash
# Quick start (both backend + frontend)
./scripts/dev.sh

# Backend only
./scripts/dev.sh backend
./scripts/run-local.sh --backend-only

# Frontend only  
./scripts/dev.sh frontend
./scripts/run-local.sh --frontend-only

# Custom port
./scripts/run-local.sh --port 8080

# Show status
./scripts/run-local.sh --status

# Stop all services
./scripts/run-local.sh --stop
./scripts/dev.sh stop
```

### Build Commands
```bash
# Development build
./scripts/build-local.sh

# Development build with tests and clean
./scripts/build-local.sh --clean --with-tests --package

# Production build
./scripts/build-production.sh

# Production build with options
./scripts/build-production.sh --with-tests --no-docker
```

### Deploy Commands
```bash
# Local traditional deployment
./scripts/deploy.sh local traditional

# Remote deployment
./scripts/deploy.sh remote docker -h server.com -u root

# Docker Compose deployment
./scripts/deploy.sh docker compose

# Kubernetes deployment
./scripts/deploy.sh k8s docker
```

### Management Commands
```bash
# Setup
./scripts/manage.sh setup dev       # Development setup
./scripts/manage.sh setup prod      # Production setup

# Run
./scripts/manage.sh run             # Run both
./scripts/manage.sh run backend     # Backend only
./scripts/manage.sh run frontend    # Frontend only

# Build
./scripts/manage.sh build dev       # Development build
./scripts/manage.sh build prod      # Production build

# Deploy
./scripts/manage.sh deploy local    # Local deployment

# Control
./scripts/manage.sh start dev       # Start development
./scripts/manage.sh stop all        # Stop all services
./scripts/manage.sh status          # Show status
./scripts/manage.sh logs            # Show logs

# Testing
./scripts/manage.sh test all        # Run all tests
./scripts/manage.sh clean           # Clean builds
```

## 🔧 Configuration

### Environment Variables

#### Development (.env)
```bash
# Backend
DATABASE_URL=sqlite:///./app.db
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true
SECRET_KEY=dev-secret-key

# Frontend
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
NODE_ENV=development
```

#### Production (.env)
```bash
# Backend
DATABASE_URL=postgresql://user:pass@localhost/db
API_HOST=127.0.0.1
API_PORT=8000
DEBUG=false
SECRET_KEY=secure-random-key
REDIS_URL=redis://localhost:6379

# Frontend
REACT_APP_API_URL=/api
REACT_APP_WS_URL=/ws
NODE_ENV=production
```

### System Requirements

#### Development
- Node.js 16+
- Python 3.8+
- Git
- 4GB RAM
- 5GB disk space

#### Production
- Ubuntu 20.04+ / CentOS 8+
- 2GB+ RAM
- 10GB+ disk space
- Root access

## 🐳 Docker Support

### Development with Docker
```bash
# Build and run
docker-compose up -d

# Or use scripts
./scripts/deploy.sh docker compose
```

### Production Docker
```bash
# Build production images
./scripts/build-production.sh

# Deploy with Docker
./scripts/deploy.sh docker compose
```

## 🔍 Troubleshooting

### Common Issues

#### Permission Denied
```bash
./scripts/make-executable.sh
chmod +x scripts/*.sh
```

#### Port Already in Use
```bash
./scripts/run-local.sh --port 8080
./scripts/manage.sh stop all
```

#### Dependencies Missing
```bash
./scripts/setup-local.sh
cd frontend && npm install
cd backend && source venv/bin/activate && pip install -r requirements.txt
```

#### Build Fails
```bash
./scripts/build-local.sh --clean
./scripts/manage.sh clean
```

#### Services Not Starting
```bash
./scripts/manage.sh status
./scripts/manage.sh logs
./scripts/run-local.sh --status
```

### Debug Commands
```bash
# Test all scripts
./scripts/test-scripts.sh

# Show interactive demo
./scripts/demo.sh

# Check specific script help
./scripts/run-local.sh --help
./scripts/manage.sh --help
```

### Log Locations

#### Development
- Backend: Console output
- Frontend: Console output

#### Production
- Backend: `/var/log/facebook-automation/backend.log`
- Worker: `journalctl -u facebook-automation-worker`
- Nginx: `/var/log/nginx/`

## 📚 Advanced Usage

### Custom Configurations
```bash
# Custom backend port
./scripts/run-local.sh --port 9000

# Skip database setup
./scripts/run-local.sh --no-db

# Build with specific options
./scripts/build-production.sh --with-tests --no-clean
```

### Multiple Environments
```bash
# Development
./scripts/manage.sh setup dev
./scripts/manage.sh run

# Staging
./scripts/deploy.sh remote docker -h staging.server.com

# Production
./scripts/deploy.sh remote traditional -h prod.server.com
```

### CI/CD Integration
```bash
# In CI pipeline
./scripts/test-scripts.sh
./scripts/build-production.sh --with-tests
./scripts/deploy.sh remote docker -h $DEPLOY_HOST -u $DEPLOY_USER
```

## 🎯 Best Practices

### Development
1. Always run `./scripts/setup-local.sh` after cloning
2. Use `./scripts/dev.sh` for daily development
3. Run tests before committing: `./scripts/manage.sh test all`
4. Clean builds periodically: `./scripts/manage.sh clean`

### Production
1. Use `./scripts/build-production.sh` for production builds
2. Test deployment locally first: `./scripts/deploy.sh local traditional`
3. Monitor services: `./scripts/manage.sh status`
4. Regular backups of data directories

### Security
1. Change default passwords in production
2. Use environment variables for secrets
3. Configure SSL/TLS for production
4. Regular security updates

## 📞 Support

### Getting Help
```bash
# Script-specific help
./scripts/manage.sh --help
./scripts/run-local.sh --help
./scripts/deploy.sh --help

# Interactive demo
./scripts/demo.sh

# Test all scripts
./scripts/test-scripts.sh

# Read documentation
cat scripts/README.md
```

### Reporting Issues
1. Run `./scripts/test-scripts.sh` to identify issues
2. Check logs with `./scripts/manage.sh logs`
3. Verify system requirements
4. Check file permissions

---

**🎉 Happy Coding!** 

Bộ scripts này được thiết kế để làm cho việc development và deployment trở nên đơn giản và tự động hóa. Nếu có vấn đề gì, hãy kiểm tra troubleshooting section hoặc chạy `./scripts/demo.sh` để xem hướng dẫn chi tiết.
