# Visible Browser Scraping Guide

## Overview
Hệ thống scraping đã được cập nhật để hiển thị browser trong quá trình scraping và có enhanced logging để theo dõi chi tiết.

## Features Added

### 🖥️ **Visible Browser Mode**
- Browser window luôn hiển thị (headless=False)
- Window được maximize và brought to front
- <PERSON><PERSON> thể theo dõi real-time human-like behaviors
- Browser window positioned optimally cho monitoring

### 📊 **Enhanced Logging System**
- **DEBUG Level**: Chi tiết từng bước scroll, extraction
- **INFO Level**: Progress updates, statistics
- **WARNING Level**: Rate limiting, issues
- **ERROR Level**: Serious problems

### 👀 **Real-time Monitoring**
- Xem natural scrolling patterns
- Theo dõi mouse movements
- <PERSON>uan sát reading pauses
- Monitor viewport adjustments

## How to Use

### 1. **Quick Demo Test**
```bash
# Run demo script
python test_visible_scraping.py

# Follow prompts:
# - Enter Facebook post URL
# - Set max comments to scrape
# - Choose logging level
# - Watch browser window
```

### 2. **Through Web Interface**
```bash
# Start backend
cd backend && python main.py

# Start frontend  
cd frontend && npm start

# Create scraping task:
# - Browser window sẽ tự động hiển thị
# - Monitor logs trong console
# - Watch browser behaviors
```

### 3. **Programmatic Usage**
```python
from app.services.facebook_scraper import FacebookScraper

scraper = FacebookScraper()

# Create visible session
crawler = await scraper.create_scraping_session(profile_id=1, task_id="test")

# Scrape with visible browser
results = await scraper.scrape_facebook_post(
    crawler=crawler,
    post_url="https://facebook.com/groups/123/posts/456",
    scraping_types=[ScrapingType.COMMENTS],
    max_results=100
)
```

## What You'll See

### 🖥️ **In Browser Window:**
- **Natural scrolling**: Variable distance (200-800px)
- **Velocity curves**: Fast start → slow end
- **Reading pauses**: 2-5 seconds between scrolls
- **Mouse movements**: Hover over comments naturally
- **Micro-adjustments**: Small scroll corrections
- **Viewport changes**: Occasional scroll up/down

### 📊 **In Console Logs:**

#### **INFO Level Logs:**
```
🚀 Creating visible browser session for profile 1
👤 User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)...
📱 Viewport: {'width': 1366, 'height': 768}
🔒 Proxy: 192.168.1.100:8080
🎯 Starting comment scraping for: https://facebook.com/...
🔄 Starting human-like scrolling for comments...
✅ Found 25 comments after scroll 3 (+5 new)
📊 Extracted 5 new comments. Total: 15
```

#### **DEBUG Level Logs:**
```
📜 Scroll #3: 456px in 4 steps
   🏃 Starting scroll (fast phase)
   📖 Reading pause: 3.2s
   🔧 Micro-adjustment: 25px down
   📍 Position: 1200px → 1656px
   ⏱️  Next scroll in: 2.1s
🔍 Extracting comment data...
   👤 Name: John Doe
   🔗 Profile URL: /groups/123/user/100001234567890/
   🆔 UID: 100001234567890
   💬 Content: "This is a great post..."
   ✅ Successfully extracted: John Doe (UID: 100001234567890)
```

## Monitoring Tips

### 🎯 **What to Watch For:**

#### **Good Behaviors (Human-like):**
- ✅ Smooth, variable scrolling
- ✅ Mouse cursor moving naturally
- ✅ Pauses between actions
- ✅ Occasional direction changes
- ✅ Reading-like patterns

#### **Red Flags (Bot-like):**
- ❌ Robotic, uniform scrolling
- ❌ No mouse movements
- ❌ Too fast actions
- ❌ Perfect timing patterns
- ❌ No pauses or breaks

### 📊 **Performance Indicators:**

#### **Extraction Success:**
```
📊 Processing results:
   ✅ Valid comments: 45
   ❌ Invalid comments: 3
   📈 Success rate: 93.8%
```

#### **Rate Limiting Status:**
```
🔍 Checking for rate limiting...
✅ No rate limiting detected
⚠️  Rate limiting detected!
☕ Cooling down for 120.5 seconds...
```

## Logging Configuration

### **Set Logging Level:**
```python
import logging

# For detailed monitoring
logging.getLogger('app.services.facebook_scraper').setLevel(logging.DEBUG)

# For general progress
logging.getLogger('app.services.facebook_scraper').setLevel(logging.INFO)
```

### **Log to File:**
```python
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console
        logging.FileHandler('scraping.log')  # File
    ]
)
```

## Troubleshooting

### **Browser Not Visible:**
- Check `headless=False` in browser config
- Verify display environment (not SSH without X11)
- Try `await page.bring_to_front()`

### **No Logs Appearing:**
- Set logging level to DEBUG
- Check logger name: `app.services.facebook_scraper`
- Verify log handlers are configured

### **Scrolling Too Fast:**
- Increase delay ranges in `_perform_natural_scroll`
- Add more reading pauses
- Check random intervals

### **Rate Limiting Issues:**
- Monitor for detection messages
- Increase cooldown times
- Use more conservative scroll patterns

## Advanced Configuration

### **Customize Scroll Behavior:**
```python
# In _perform_natural_scroll method
scroll_distance = random.randint(150, 600)  # Smaller range
reading_pause = random.uniform(3.0, 7.0)   # Longer pauses
```

### **Adjust Mouse Movements:**
```python
# In _simulate_mouse_hover method
if random.random() < 0.6:  # Increase frequency
    await self._simulate_mouse_hover(page)
```

### **Modify Break Patterns:**
```python
# In _human_like_scrolling method
if scroll_count % random.randint(8, 12) == 0:  # More frequent breaks
    await asyncio.sleep(random.uniform(8.0, 15.0))  # Longer breaks
```

## Best Practices

### **For Development:**
1. Always use visible browser mode
2. Set DEBUG logging level
3. Monitor both browser and console
4. Test with small datasets first
5. Verify UID extraction accuracy

### **For Production:**
1. Use INFO logging level
2. Monitor rate limiting carefully
3. Implement proper error handling
4. Log results to files
5. Set appropriate timeouts

### **For Debugging:**
1. Use DEBUG level logging
2. Add breakpoints in key methods
3. Inspect extracted data structure
4. Verify XPath selectors
5. Test with different post types

## Sample Output

### **Successful Scraping Session:**
```
🚀 Creating visible browser session for profile 1
✅ Browser window brought to front and positioned
🎯 Starting comment scraping for: https://facebook.com/groups/123/posts/456
🔄 Starting human-like scrolling for comments...
✅ Found 15 comments after scroll 1 (+15 new)
📊 Extracted 12 new comments. Total: 12
   👤 1. John Doe (UID: 100001234567890)
      💬 "Great post! Thanks for sharing..."
   👤 2. Jane Smith (UID: 100009876543210)
      💬 "I totally agree with this..."
   👤 3. Mike Johnson (UID: 100005555555555)
      💬 "Has anyone tried this approach..."
☕ Taking a longer break for 7.3 seconds...
✅ Found 28 comments after scroll 2 (+13 new)
📊 Processing results:
   ✅ Valid comments: 25
   ❌ Invalid comments: 3
   📈 Success rate: 89.3%
🎉 Scraping completed successfully!
```

Hệ thống này cho phép bạn theo dõi real-time quá trình scraping và đảm bảo behaviors giống người thật.
