Collecting redis
  Using cached redis-6.2.0-py3-none-any.whl.metadata (10 kB)
Collecting async-timeout>=4.0.3 (from redis)
  Using cached async_timeout-5.0.1-py3-none-any.whl.metadata (5.1 kB)
Using cached redis-6.2.0-py3-none-any.whl (278 kB)
Using cached async_timeout-5.0.1-py3-none-any.whl (6.2 kB)
Installing collected packages: async-timeout, redis

Successfully installed async-timeout-5.0.1 redis-6.2.0
