#!/usr/bin/env python3
"""
Database initialization script
Run this to create all database tables
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import init_db, engine
from app.core.logger import setup_logger

logger = setup_logger(__name__)


async def main():
    """Initialize database tables"""
    try:
        logger.info("Initializing database...")
        
        # Initialize database
        await init_db()
        
        logger.info("Database initialization completed successfully!")
        
        # Test database connection
        from app.core.database import AsyncSessionLocal
        async with AsyncSessionLocal() as session:
            logger.info("Database connection test successful")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)
    finally:
        # Close engine
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(main())
