#!/usr/bin/env python3
"""
Test script for Camoufox integration
"""

import sys
import asyncio

def test_imports():
    """Test all imports"""
    print("🔧 Testing imports...")
    
    try:
        import camoufox
        print("✅ camoufox imported successfully")
    except Exception as e:
        print(f"❌ camoufox import failed: {e}")
        return False
    
    try:
        from camoufox import AsyncCamoufox, launch_options
        print("✅ AsyncCamoufox and launch_options imported successfully")
    except Exception as e:
        print(f"❌ AsyncCamoufox import failed: {e}")
        return False
    
    try:
        from app.services.camoufox_manager import CamoufoxBrowserManager
        print("✅ CamoufoxBrowserManager imported successfully")
    except Exception as e:
        print(f"❌ CamoufoxBrowserManager import failed: {e}")
        return False
    
    try:
        from app.services.fingerprint_generator import FingerprintGenerator
        print("✅ FingerprintGenerator imported successfully")
    except Exception as e:
        print(f"❌ FingerprintGenerator import failed: {e}")
        return False
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        print("✅ FacebookScraper imported successfully")
    except Exception as e:
        print(f"❌ FacebookScraper import failed: {e}")
        return False
    
    return True

async def test_camoufox_manager():
    """Test CamoufoxBrowserManager basic functionality"""
    print("\n🔧 Testing CamoufoxBrowserManager...")
    
    try:
        from app.services.camoufox_manager import CamoufoxBrowserManager
        
        manager = CamoufoxBrowserManager()
        print("✅ CamoufoxBrowserManager instance created")
        
        # Test config creation
        config = await manager.create_browser_config(
            profile_path="/tmp/test_profile",
            proxy_config=None,
            fingerprint={'user_agent': 'test'},
            headless=True
        )
        print("✅ Browser config created successfully")
        print(f"   Config keys: {list(config.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ CamoufoxBrowserManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Camoufox Integration Test")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed!")
        return False
    
    print("\n✅ All imports successful!")
    
    # Test components
    if not await test_camoufox_manager():
        print("\n❌ CamoufoxBrowserManager test failed!")
        return False
    
    print("\n🎉 All tests passed!")
    print("✅ Camoufox integration is working correctly")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
