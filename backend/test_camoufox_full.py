#!/usr/bin/env python3
"""
Full integration test for Camoufox implementation
"""

import asyncio
import sys
import json
from pathlib import Path

async def test_profile_manager_integration():
    """Test ProfileManager with Camoufox integration"""
    print("\n🔧 Testing ProfileManager with Camoufox...")
    
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        # Create profile manager
        manager = AntidetectProfileManager()
        print("✅ AntidetectProfileManager created")
        
        # Check if CamoufoxBrowserManager is integrated
        if hasattr(manager, 'camoufox_manager'):
            print("✅ CamoufoxBrowserManager integrated successfully")
        else:
            print("❌ CamoufoxBrowserManager not found in ProfileManager")
            return False
        
        # Test fingerprint generation
        fingerprint = await manager.fingerprint_generator.generate_fingerprint(use_firefox_only=True)
        print("✅ Firefox fingerprint generated successfully")
        print(f"   User Agent: {fingerprint.get('user_agent', 'N/A')[:60]}...")
        
        # Test browser config creation (mock)
        config = manager._create_browser_config(
            proxy_config={'type': 'no_proxy'},
            fingerprint=fingerprint
        )
        print("✅ Browser config created successfully")
        print(f"   Browser type: {config.get('browser_type', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ ProfileManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_facebook_scraper_integration():
    """Test FacebookScraper with Camoufox integration"""
    print("\n🔧 Testing FacebookScraper with Camoufox...")
    
    try:
        from app.services.facebook_scraper import FacebookScraper, CamoufoxCrawlerWrapper
        
        # Create scraper
        scraper = FacebookScraper()
        print("✅ FacebookScraper created")
        
        # Check if it uses AntidetectProfileManager with Camoufox
        if hasattr(scraper.profile_manager, 'camoufox_manager'):
            print("✅ FacebookScraper integrated with CamoufoxBrowserManager")
        else:
            print("❌ FacebookScraper not properly integrated with Camoufox")
            return False
        
        # Test extraction strategies
        if scraper.extraction_strategies:
            print(f"✅ Extraction strategies loaded: {list(scraper.extraction_strategies.keys())}")
        else:
            print("❌ No extraction strategies found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FacebookScraper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_compatibility():
    """Test API route compatibility"""
    print("\n🔧 Testing API compatibility...")
    
    try:
        # Test if we can import the routes without errors
        from app.api.routes.profiles import router
        print("✅ Profile API routes imported successfully")
        
        # Check if the routes are properly configured
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/",
            "/{profile_id}/launch-browser", 
            "/{profile_id}/open-facebook",
            "/{profile_id}/complete-login",
            "/{profile_id}/browser-status",
            "/{profile_id}/close-browser"
        ]
        
        for expected_route in expected_routes:
            if expected_route in routes:
                print(f"✅ Route {expected_route} found")
            else:
                print(f"❌ Route {expected_route} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ API compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_camoufox_config_generation():
    """Test Camoufox configuration generation"""
    print("\n🔧 Testing Camoufox configuration generation...")
    
    try:
        from app.services.camoufox_manager import CamoufoxBrowserManager
        from app.services.fingerprint_generator import FingerprintGenerator
        
        # Create manager and generator
        manager = CamoufoxBrowserManager()
        generator = FingerprintGenerator()
        
        # Generate Firefox fingerprint
        fingerprint = await generator.generate_fingerprint(use_firefox_only=True)
        print("✅ Firefox fingerprint generated")
        
        # Create Camoufox config
        config = await manager.create_browser_config(
            profile_path="/tmp/test_profile",
            proxy_config={
                'type': 'http',
                'host': 'proxy.example.com',
                'port': 8080,
                'username': 'user',
                'password': 'pass'
            },
            fingerprint=fingerprint,
            headless=True
        )
        
        print("✅ Camoufox config created successfully")
        
        # Verify config structure
        required_keys = ['headless', 'geoip', 'humanize', 'config']
        for key in required_keys:
            if key in config:
                print(f"✅ Config key '{key}' present")
            else:
                print(f"❌ Config key '{key}' missing")
                return False
        
        # Check if proxy is configured
        if 'proxy' in config:
            print("✅ Proxy configuration included")
        else:
            print("ℹ️  No proxy configuration (expected for this test)")
        
        return True
        
    except Exception as e:
        print(f"❌ Camoufox config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🦊 Camoufox Full Integration Test")
    print("=" * 60)
    
    tests = [
        ("ProfileManager Integration", test_profile_manager_integration),
        ("FacebookScraper Integration", test_facebook_scraper_integration),
        ("API Compatibility", test_api_compatibility),
        ("Camoufox Config Generation", test_camoufox_config_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if await test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Camoufox integration is working correctly")
        print("🚀 Ready for production use!")
        return True
    else:
        print("❌ Some tests failed")
        print("🔧 Please review the failed tests and fix issues")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
