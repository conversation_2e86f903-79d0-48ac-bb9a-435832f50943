#!/usr/bin/env python3
"""
Test full workflow: Profile creation, Facebook login, cookie management
"""

import asyncio
import sys
import json
import os
from pathlib import Path

async def test_profile_creation():
    """Test profile creation with Camoufox"""
    print("\n🔧 Testing Profile Creation...")
    
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        manager = AntidetectProfileManager()
        
        # Test profile creation
        profile_data = {
            'name': 'Test Camoufox Profile',
            'proxy_config': {
                'type': 'no_proxy',
                'host': None,
                'port': None,
                'username': None,
                'password': None
            }
        }
        
        result = await manager.create_profile(profile_data)
        
        if result.get('success'):
            print("✅ Profile created successfully")
            print(f"   Profile path: {result.get('profile_path')}")
            print(f"   Fingerprint generated: {'Yes' if result.get('fingerprint') else 'No'}")
            return result
        else:
            print(f"❌ Profile creation failed: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ Profile creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_browser_launch(profile_path):
    """Test Camoufox browser launch"""
    print("\n🚀 Testing Camoufox Browser Launch...")
    
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        manager = AntidetectProfileManager()
        
        # Test browser launch
        result = await manager.launch_browser(
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'},
            headless=False  # Visible for testing
        )
        
        if result.get('success'):
            print("✅ Camoufox browser launched successfully")
            print(f"   Browser type: {result.get('browser_type', 'unknown')}")
            print(f"   Antidetect features: {result.get('antidetect_features', [])}")
            
            # Get profile ID for cleanup
            profile_id = os.path.basename(profile_path)
            return profile_id
        else:
            print(f"❌ Browser launch failed: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ Browser launch test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_facebook_login(profile_path):
    """Test Facebook login functionality"""
    print("\n🌐 Testing Facebook Login...")
    
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        manager = AntidetectProfileManager()
        profile_id = os.path.basename(profile_path)
        
        # Test Facebook login
        result = await manager.open_facebook_login(
            profile_id=profile_id,
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'}
        )
        
        if result.get('success'):
            print("✅ Facebook login page opened successfully")
            print(f"   Browser type: {result.get('browser_type', 'unknown')}")
            print(f"   Auto-launched: {result.get('auto_launched', False)}")
            print(f"   Cookies loaded: {result.get('cookies_loaded', False)}")
            print(f"   Page URL: {result.get('page_url', 'N/A')}")
            
            # Check if user wants to test manual login
            print("\n📝 Manual Facebook Login Test:")
            print("   1. The Camoufox browser should now be open")
            print("   2. Navigate to Facebook and login manually")
            print("   3. After successful login, press Enter to continue...")
            
            input("⏸️  Press Enter when you have completed Facebook login...")
            
            # Test complete login (save cookies)
            complete_result = await manager.complete_facebook_login(
                profile_id=profile_id,
                profile_path=profile_path
            )
            
            if complete_result.get('success'):
                print("✅ Facebook login completed and cookies saved")
                print(f"   Logged in: {complete_result.get('logged_in', False)}")
                print(f"   Browser status: {complete_result.get('browser_status', 'unknown')}")
                
                # Check if cookies were saved
                cookies_file = Path(profile_path) / 'facebook_cookies.json'
                if cookies_file.exists():
                    print(f"✅ Facebook cookies saved to: {cookies_file}")
                    with open(cookies_file, 'r') as f:
                        cookies = json.load(f)
                    print(f"   Number of cookies: {len(cookies)}")
                else:
                    print("⚠️  No cookies file found")
                
                return True
            else:
                print(f"❌ Complete login failed: {complete_result.get('message')}")
                return False
        else:
            print(f"❌ Facebook login failed: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Facebook login test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_browser_status_and_cleanup(profile_path):
    """Test browser status and cleanup"""
    print("\n🔍 Testing Browser Status and Cleanup...")
    
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        manager = AntidetectProfileManager()
        profile_id = os.path.basename(profile_path)
        
        # Test browser status
        status_result = await manager.get_browser_status(profile_id)
        
        if status_result.get('success'):
            print("✅ Browser status retrieved successfully")
            print(f"   Browser active: {status_result.get('browser_active', False)}")
            print(f"   Browser type: {status_result.get('browser_type', 'unknown')}")
            
            if status_result.get('browser_active'):
                # Test browser cleanup
                close_result = await manager.close_browser(profile_id)
                
                if close_result.get('success'):
                    print("✅ Browser closed successfully")
                    return True
                else:
                    print(f"❌ Browser close failed: {close_result.get('message')}")
                    return False
            else:
                print("ℹ️  Browser was not active")
                return True
        else:
            print(f"❌ Browser status check failed: {status_result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Browser status test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test workflow"""
    print("🦊 Camoufox Full Workflow Test")
    print("=" * 60)
    print("This test will:")
    print("1. Create a new profile with Camoufox")
    print("2. Launch Camoufox browser")
    print("3. Open Facebook login page")
    print("4. Test manual Facebook login and cookie saving")
    print("5. Test browser status and cleanup")
    print()
    
    # Step 1: Profile Creation
    profile_result = await test_profile_creation()
    if not profile_result:
        print("\n❌ Profile creation failed, stopping test")
        return False
    
    profile_path = profile_result.get('profile_path')
    
    try:
        # Step 2: Browser Launch
        profile_id = await test_browser_launch(profile_path)
        if not profile_id:
            print("\n❌ Browser launch failed, stopping test")
            return False
        
        # Step 3: Facebook Login
        facebook_success = await test_facebook_login(profile_path)
        if not facebook_success:
            print("\n⚠️  Facebook login test had issues, but continuing...")
        
        # Step 4: Browser Status and Cleanup
        cleanup_success = await test_browser_status_and_cleanup(profile_path)
        if not cleanup_success:
            print("\n⚠️  Cleanup had issues")
        
        print("\n" + "=" * 60)
        print("🎉 Full workflow test completed!")
        print("✅ Profile creation: SUCCESS")
        print("✅ Browser launch: SUCCESS")
        print(f"{'✅' if facebook_success else '⚠️ '} Facebook login: {'SUCCESS' if facebook_success else 'PARTIAL'}")
        print(f"{'✅' if cleanup_success else '⚠️ '} Cleanup: {'SUCCESS' if cleanup_success else 'PARTIAL'}")
        
        return True
        
    except Exception as e:
        print(f"\n💥 Workflow test crashed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
