#!/usr/bin/env python3
"""
Test Facebook modal scraping with new XPath selectors
"""

import asyncio
import sys
import json
from pathlib import Path

async def test_modal_scraping():
    """Test modal scraping functionality"""
    print("\n🦊 Testing Facebook Modal Scraping with Camoufox")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        
        # Create scraper instance
        scraper = FacebookScraper()
        print("✅ FacebookScraper created")
        
        # Check modal selectors
        print("\n🔍 Modal Selectors Configuration:")
        for key, value in scraper.modal_selectors.items():
            print(f"   {key}: {value[:80]}...")
        
        # Test profile creation for scraping
        profile_data = {
            'name': 'Modal Scraping Test Profile',
            'proxy_config': {
                'type': 'no_proxy',
                'host': None,
                'port': None,
                'username': None,
                'password': None
            }
        }
        
        print("\n📝 Creating test profile...")
        profile_result = await scraper.profile_manager.create_profile(profile_data)
        
        if not profile_result.get('success'):
            print(f"❌ Profile creation failed: {profile_result.get('message')}")
            return False
        
        profile_path = profile_result.get('profile_path')
        # Extract profile ID from path
        import os
        profile_id = os.path.basename(profile_path) if profile_path else 'test_profile'

        print(f"✅ Profile created: {profile_path}")
        print(f"   Profile ID: {profile_id}")

        # For testing, we'll create a simple test without database dependency
        print("\n🚀 Testing modal scraping directly...")

        # Test modal scraping method directly without full session
        try:
            from app.services.camoufox_manager import CamoufoxBrowserManager

            camoufox_manager = CamoufoxBrowserManager()

            # Create browser config
            config = await camoufox_manager.create_browser_config(
                profile_path=profile_path,
                proxy_config={'type': 'no_proxy'},
                fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
                headless=False
            )

            # Launch browser
            launch_result = await camoufox_manager.launch_browser(
                profile_id=profile_id,
                profile_path=profile_path,
                proxy_config={'type': 'no_proxy'},
                fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
                headless=False
            )

            if not launch_result.get('success'):
                print(f"❌ Failed to launch browser: {launch_result.get('message')}")
                return False

            context = await camoufox_manager.get_browser_context(profile_id)
            if not context:
                print("❌ Failed to get browser context")
                return False

            print("✅ Browser launched successfully")
        
            # Test Facebook post URL (you can replace with actual post URL)
            test_post_url = input("\n📝 Enter Facebook post URL to test (or press Enter for demo): ").strip()

            if not test_post_url:
                test_post_url = "https://www.facebook.com/groups/591054007361950/posts/1234567890/"
                print(f"Using demo URL: {test_post_url}")

            # Test modal scraping
            print(f"\n🔄 Testing modal scraping for: {test_post_url}")

            # Test the modal scraping method directly
            comments = await scraper._scrape_comments_with_modal_scroll(context, test_post_url, max_results=10)
        
            print(f"\n📊 Modal Scraping Results:")
            print(f"   Comments found: {len(comments)}")

            if comments:
                print("\n👥 Sample Comments:")
                for i, comment in enumerate(comments[:3]):  # Show first 3
                    print(f"   {i+1}. {comment.get('full_name', 'Unknown')} (UID: {comment.get('facebook_uid', 'N/A')})")
                    print(f"      Content: {comment.get('interaction_content', 'No content')[:50]}...")
                    print(f"      Profile: {comment.get('profile_url', 'No URL')}")
                    print()
            else:
                print("⚠️  No comments extracted")

            # Cleanup
            print("\n🧹 Cleaning up...")
            await camoufox_manager.close_browser(profile_id)
            print("✅ Browser closed")

        except Exception as e:
            print(f"❌ Browser test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Modal scraping test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_xpath_validation():
    """Test XPath selectors validation"""
    print("\n🔍 Testing XPath Selectors Validation")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        scraper = FacebookScraper()
        
        print("📋 Configured XPath Selectors:")
        print()
        
        selectors = scraper.modal_selectors
        
        for name, xpath in selectors.items():
            print(f"🎯 {name}:")
            print(f"   XPath: {xpath}")
            
            # Basic XPath validation
            if xpath.startswith('//') or xpath.startswith('/html'):
                print("   ✅ Valid XPath format")
            else:
                print("   ⚠️  Unusual XPath format")
            
            if 'user' in xpath and 'href' in xpath:
                print("   ✅ Contains user link pattern")
            elif name in ['user_links', 'avatar_links']:
                print("   ⚠️  User link selector may need review")
            
            print()
        
        print("✅ XPath validation completed")
        return True
        
    except Exception as e:
        print(f"❌ XPath validation failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🦊 Facebook Modal Scraping Test Suite")
    print("=" * 60)
    
    # Test 1: XPath validation
    xpath_success = await test_xpath_validation()
    
    # Test 2: Modal scraping
    modal_success = await test_modal_scraping()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   XPath Validation: {'✅ PASS' if xpath_success else '❌ FAIL'}")
    print(f"   Modal Scraping: {'✅ PASS' if modal_success else '❌ FAIL'}")
    
    if xpath_success and modal_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Modal scraping with new XPaths is ready!")
        return True
    else:
        print("\n❌ Some tests failed")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
