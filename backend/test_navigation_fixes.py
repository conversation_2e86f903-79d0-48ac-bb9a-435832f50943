#!/usr/bin/env python3
"""
Test navigation fixes for Facebook and scraping
"""

import asyncio
import sys
import time

async def test_facebook_navigation():
    """Test Facebook navigation without multiple reloads"""
    print("\n🌐 Testing Facebook Navigation Fixes")
    print("=" * 60)
    
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        # Create profile manager
        manager = AntidetectProfileManager()
        
        # Create test profile
        profile_data = {
            'name': 'Navigation Test Profile',
            'proxy_config': {
                'type': 'no_proxy',
                'host': None,
                'port': None,
                'username': None,
                'password': None
            }
        }
        
        print("📝 Creating test profile...")
        profile_result = await manager.create_profile(profile_data)
        
        if not profile_result.get('success'):
            print(f"❌ Profile creation failed: {profile_result.get('message')}")
            return False
        
        profile_path = profile_result.get('profile_path')
        import os
        profile_id = os.path.basename(profile_path)
        
        print(f"✅ Profile created: {profile_id}")
        
        # Test browser launch
        print("\n🚀 Testing browser launch...")
        launch_result = await manager.launch_browser(
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'},
            headless=False
        )
        
        if not launch_result.get('success'):
            print(f"❌ Browser launch failed: {launch_result.get('message')}")
            return False
        
        print("✅ Browser launched successfully")
        
        # Test Facebook navigation
        print("\n🌐 Testing Facebook navigation (should only navigate once)...")
        start_time = time.time()
        
        facebook_result = await manager.open_facebook_login(
            profile_id=profile_id,
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'}
        )
        
        end_time = time.time()
        navigation_time = end_time - start_time
        
        if facebook_result.get('success'):
            print(f"✅ Facebook opened successfully")
            print(f"   Navigation time: {navigation_time:.2f} seconds")
            print(f"   Final URL: {facebook_result.get('page_url', 'N/A')}")
            print(f"   Auto-launched: {facebook_result.get('auto_launched', False)}")
            print(f"   Logged in: {facebook_result.get('logged_in', False)}")
            print(f"   Cookies loaded: {facebook_result.get('cookies_loaded', False)}")
            
            # Check if navigation was efficient (should be under 15 seconds for single navigation)
            if navigation_time < 15:
                print("✅ Navigation was efficient (no excessive reloads)")
            else:
                print("⚠️  Navigation took longer than expected (possible multiple reloads)")
        else:
            print(f"❌ Facebook navigation failed: {facebook_result.get('message')}")
            return False
        
        # Test browser status
        print("\n🔍 Testing browser status...")
        status_result = await manager.get_browser_status(profile_id)
        
        if status_result.get('success'):
            print(f"✅ Browser status: {status_result.get('browser_active', False)}")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        await manager.close_browser(profile_id)
        print("✅ Browser closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Facebook navigation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scraping_navigation():
    """Test scraping without multiple page creations"""
    print("\n📜 Testing Scraping Navigation Fixes")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        
        # Create scraper
        scraper = FacebookScraper()
        
        # Create test profile
        profile_data = {
            'name': 'Scraping Test Profile',
            'proxy_config': {
                'type': 'no_proxy',
                'host': None,
                'port': None,
                'username': None,
                'password': None
            }
        }
        
        print("📝 Creating test profile...")
        profile_result = await scraper.profile_manager.create_profile(profile_data)
        
        if not profile_result.get('success'):
            print(f"❌ Profile creation failed: {profile_result.get('message')}")
            return False
        
        profile_path = profile_result.get('profile_path')
        import os
        profile_id = os.path.basename(profile_path)
        
        print(f"✅ Profile created: {profile_id}")
        
        # Test scraping session creation
        print("\n🔧 Testing scraping session creation...")
        
        # We'll test the modal scraping method directly
        from app.services.camoufox_manager import CamoufoxBrowserManager
        
        camoufox_manager = CamoufoxBrowserManager()
        
        # Launch browser
        launch_result = await camoufox_manager.launch_browser(
            profile_id=profile_id,
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'},
            fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
            headless=False
        )
        
        if not launch_result.get('success'):
            print(f"❌ Browser launch failed: {launch_result.get('message')}")
            return False
        
        context = await camoufox_manager.get_browser_context(profile_id)
        if not context:
            print("❌ Failed to get browser context")
            return False
        
        print("✅ Browser context obtained")
        
        # Create a page for testing
        page = await context.new_page()
        await page.goto('https://www.facebook.com/', timeout=30000)
        print("✅ Initial page created and navigated")
        
        # Test modal scraping with existing page (should not create new page)
        print("\n📜 Testing modal scraping with page reuse...")
        start_time = time.time()
        
        test_url = "https://www.facebook.com/groups/591054007361950/posts/1234567890/"
        
        # Test with existing page
        comments = await scraper._scrape_comments_with_modal_scroll(
            context, test_url, max_results=5, existing_page=page
        )
        
        end_time = time.time()
        scraping_time = end_time - start_time
        
        print(f"✅ Modal scraping completed")
        print(f"   Scraping time: {scraping_time:.2f} seconds")
        print(f"   Comments found: {len(comments)}")
        print(f"   Page reused: ✅ (no new page creation)")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        await page.close()
        await camoufox_manager.close_browser(profile_id)
        print("✅ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraping navigation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🔧 Navigation and Reload Fixes Test Suite")
    print("=" * 60)
    print("Testing fixes for:")
    print("1. Facebook multiple reload issue")
    print("2. Scraping page recreation issue")
    print()
    
    # Test 1: Facebook navigation
    facebook_success = await test_facebook_navigation()
    
    # Test 2: Scraping navigation
    scraping_success = await test_scraping_navigation()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Facebook Navigation: {'✅ PASS' if facebook_success else '❌ FAIL'}")
    print(f"   Scraping Navigation: {'✅ PASS' if scraping_success else '❌ FAIL'}")
    
    if facebook_success and scraping_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Navigation fixes are working correctly!")
        print("✅ No more excessive reloads or page recreations!")
        return True
    else:
        print("\n❌ Some tests failed")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
