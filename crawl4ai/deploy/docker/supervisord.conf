[supervisord]
nodaemon=true                   ; Run supervisord in the foreground
logfile=/dev/null               ; Log supervisord output to stdout/stderr
logfile_maxbytes=0

[program:redis]
command=/usr/bin/redis-server --loglevel notice ; Path to redis-server on Alpine
user=appuser                    ; Run redis as our non-root user
autorestart=true
priority=10
stdout_logfile=/dev/stdout      ; Redirect redis stdout to container stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr      ; Redirect redis stderr to container stderr
stderr_logfile_maxbytes=0

[program:gunicorn]
command=/usr/local/bin/gunicorn --bind 0.0.0.0:11235 --workers 1 --threads 4 --timeout 1800 --graceful-timeout 30 --keep-alive 300 --log-level info --worker-class uvicorn.workers.UvicornWorker server:app
directory=/app                  ; Working directory for the app
user=appuser                    ; Run gunicorn as our non-root user
autorestart=true
priority=20
environment=PYTHONUNBUFFERED=1  ; Ensure Python output is sent straight to logs
stdout_logfile=/dev/stdout      ; Redirect gunicorn stdout to container stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr      ; Redirect gunicorn stderr to container stderr
stderr_logfile_maxbytes=0

# Optional: Add filebeat or other logging agents here if needed