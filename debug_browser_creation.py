#!/usr/bin/env python3
"""
Debug script to test browser creation and visibility
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('browser_debug.log')
    ]
)

logger = logging.getLogger(__name__)

async def test_browser_creation():
    """Test browser creation step by step"""
    
    print("🔧 Browser Creation Debug Test")
    print("=" * 50)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.core.config import settings
        
        # Initialize scraper
        scraper = FacebookScraper()
        
        # Test configuration
        profile_id = 1
        task_id = "debug_test_001"
        
        print(f"📊 Test Configuration:")
        print(f"   Profile ID: {profile_id}")
        print(f"   Task ID: {task_id}")
        print(f"   Profiles Dir: {settings.PROFILES_DIR}")
        
        # Check profiles directory
        profiles_dir = settings.PROFILES_DIR
        if not profiles_dir.exists():
            print(f"📁 Creating profiles directory: {profiles_dir}")
            profiles_dir.mkdir(parents=True, exist_ok=True)
        
        profile_path = profiles_dir / f"profile_{profile_id}"
        if not profile_path.exists():
            print(f"📁 Creating profile directory: {profile_path}")
            profile_path.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ Profile directory ready: {profile_path}")
        
        # Test browser creation
        print(f"\n🚀 Testing browser creation...")
        
        try:
            crawler = await scraper.create_scraping_session(profile_id, task_id)
            
            if crawler:
                print(f"✅ Browser created successfully!")
                print(f"🖥️  Browser window should be visible now")
                
                # Test page navigation
                print(f"\n🌐 Testing page navigation...")
                page = crawler.crawler_strategy.page
                if page:
                    print(f"✅ Page object available")
                    
                    # Navigate to a test page
                    test_url = "https://www.google.com"
                    print(f"📍 Navigating to: {test_url}")
                    
                    await page.goto(test_url)
                    title = await page.title()
                    print(f"📄 Page title: {title}")
                    
                    # Keep browser open for inspection
                    print(f"\n🔍 Browser is now open and ready for inspection")
                    print(f"   You should see a Google page in the browser window")
                    
                    keep_open = input("\nKeep browser open for inspection? (y/N): ").lower().strip()
                    if keep_open == 'y':
                        print("🖥️  Browser will remain open. Close manually when done.")
                        input("Press Enter to continue...")
                    
                    # Cleanup
                    await scraper.cleanup_session(task_id)
                    print("✅ Browser session cleaned up")
                    
                else:
                    print("❌ No page object available")
                    
            else:
                print("❌ Browser creation returned None")
                
        except Exception as browser_error:
            print(f"❌ Browser creation failed: {browser_error}")
            logger.exception("Browser creation error details:")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test error details:")

async def test_crawl4ai_directly():
    """Test crawl4ai directly without our wrapper"""
    
    print("\n🔧 Direct Crawl4AI Test")
    print("=" * 30)
    
    try:
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import BrowserConfig
        
        # Create browser config
        browser_config = BrowserConfig(
            browser_type='chromium',
            headless=False,  # Ensure visible
            viewport_width=1366,
            viewport_height=768,
            use_managed_browser=True,
            extra_args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--start-maximized',
                '--disable-infobars'
            ]
        )
        
        print(f"🚀 Creating AsyncWebCrawler directly...")
        crawler = AsyncWebCrawler(config=browser_config)
        
        print(f"🔄 Starting crawler...")
        await crawler.start()
        
        print(f"✅ Crawler started successfully!")
        print(f"🖥️  Browser window should be visible now")
        
        # Test navigation
        test_url = "https://www.example.com"
        print(f"📍 Testing navigation to: {test_url}")
        
        result = await crawler.arun(test_url)
        if result.success:
            print(f"✅ Navigation successful!")
            print(f"📄 Page title: {result.metadata.get('title', 'N/A')}")
        else:
            print(f"❌ Navigation failed: {result.error_message}")
        
        # Keep open for inspection
        keep_open = input("\nKeep browser open for inspection? (y/N): ").lower().strip()
        if keep_open == 'y':
            print("🖥️  Browser will remain open. Close manually when done.")
            input("Press Enter to continue...")
        
        # Cleanup
        await crawler.close()
        print("✅ Crawler closed")
        
    except Exception as e:
        print(f"❌ Direct crawl4ai test failed: {e}")
        logger.exception("Direct test error details:")

async def check_system_requirements():
    """Check system requirements for browser"""
    
    print("\n🔍 System Requirements Check")
    print("=" * 35)
    
    import platform
    import shutil
    
    print(f"🖥️  Operating System: {platform.system()} {platform.release()}")
    print(f"🐍 Python Version: {platform.python_version()}")
    
    # Check for Chrome/Chromium
    chrome_paths = [
        '/usr/bin/google-chrome',
        '/usr/bin/chromium-browser',
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ]
    
    chrome_found = False
    for chrome_path in chrome_paths:
        if Path(chrome_path).exists():
            print(f"✅ Chrome found: {chrome_path}")
            chrome_found = True
            break
    
    if not chrome_found:
        chrome_cmd = shutil.which('google-chrome') or shutil.which('chromium') or shutil.which('chrome')
        if chrome_cmd:
            print(f"✅ Chrome found in PATH: {chrome_cmd}")
            chrome_found = True
    
    if not chrome_found:
        print(f"⚠️  Chrome/Chromium not found in standard locations")
        print(f"   This might cause browser creation issues")
    
    # Check display environment
    import os
    if platform.system() == "Linux":
        display = os.environ.get('DISPLAY')
        if display:
            print(f"✅ Display environment: {display}")
        else:
            print(f"⚠️  No DISPLAY environment variable (headless environment?)")
    
    # Check available ports
    import socket
    test_ports = [9222, 9223, 9224, 9225]
    available_ports = []
    
    for port in test_ports:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        if result != 0:  # Port is available
            available_ports.append(port)
    
    print(f"🔌 Available debugging ports: {available_ports}")
    
    if len(available_ports) == 0:
        print(f"⚠️  No debugging ports available, this might cause issues")

async def main():
    """Main debug function"""
    
    print("🐛 Browser Creation Debug Tool")
    print("=" * 60)
    
    # Check system requirements
    await check_system_requirements()
    
    # Test direct crawl4ai
    await test_crawl4ai_directly()
    
    # Test our wrapper
    await test_browser_creation()
    
    print(f"\n📄 Detailed logs saved to 'browser_debug.log'")
    print(f"✅ Debug test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Debug test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
