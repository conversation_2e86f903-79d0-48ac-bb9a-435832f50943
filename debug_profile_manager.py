#!/usr/bin/env python3
"""
Debug ProfileManager integration issues
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def debug_profile_manager():
    """Debug ProfileManager functionality"""
    
    print("🔍 ProfileManager Debug Test")
    print("=" * 40)
    
    try:
        # Test 1: Import ProfileManager
        print("📦 Test 1: Import ProfileManager")
        try:
            from app.services.profile_manager import profile_manager
            print("✅ ProfileManager imported successfully")
        except Exception as import_error:
            print(f"❌ ProfileManager import failed: {import_error}")
            return False
        
        # Test 2: Check ProfileManager state
        print("\n🔧 Test 2: ProfileManager State")
        print(f"   Active browsers: {list(profile_manager.active_browsers.keys())}")
        print(f"   ProfileManager type: {type(profile_manager)}")
        
        # Test 3: Get profile from database
        print("\n📊 Test 3: Database Profile Lookup")
        try:
            from app.core.database import AsyncSessionLocal
            from app.models.profile import Profile
            from sqlalchemy import select
            
            async with AsyncSessionLocal() as db:
                result = await db.execute(select(Profile))
                profiles = result.scalars().all()
                
                if profiles:
                    test_profile = profiles[0]
                    print(f"✅ Found test profile: {test_profile.name}")
                    print(f"   ID: {test_profile.id}")
                    print(f"   Path: {test_profile.profile_path}")
                    print(f"   Proxy: {test_profile.proxy_type}")
                    
                    # Test 4: Test ProfileManager.open_facebook_login
                    print(f"\n🌐 Test 4: ProfileManager.open_facebook_login")
                    
                    # Prepare config
                    proxy_config = {
                        'type': test_profile.proxy_type or 'no_proxy',
                        'host': test_profile.proxy_host,
                        'port': test_profile.proxy_port,
                        'username': test_profile.proxy_username,
                        'password': test_profile.proxy_password
                    }
                    
                    browser_profile_id = Path(test_profile.profile_path).name
                    print(f"   Browser profile ID: {browser_profile_id}")
                    print(f"   Profile path: {test_profile.profile_path}")
                    print(f"   Proxy config: {proxy_config}")
                    
                    # Create profile directory if needed
                    profile_path = Path(test_profile.profile_path)
                    if not profile_path.exists():
                        print(f"   📁 Creating profile directory: {profile_path}")
                        profile_path.mkdir(parents=True, exist_ok=True)
                    
                    try:
                        print(f"   🚀 Calling open_facebook_login...")
                        open_result = await profile_manager.open_facebook_login(
                            browser_profile_id,
                            test_profile.profile_path,
                            proxy_config
                        )
                        
                        print(f"   📊 Result: {open_result}")
                        
                        if open_result.get('success'):
                            print("   ✅ open_facebook_login succeeded")
                            
                            # Check active browsers
                            print(f"   🔍 Active browsers after call: {list(profile_manager.active_browsers.keys())}")
                            
                            if browser_profile_id in profile_manager.active_browsers:
                                print("   ✅ Browser context found in active_browsers")
                                browser_info = profile_manager.active_browsers[browser_profile_id]
                                print(f"   📊 Browser info keys: {browser_info.keys()}")
                                
                                # Test browser context
                                context = browser_info.get('context')
                                if context:
                                    print("   ✅ Browser context available")
                                    
                                    # Test page creation
                                    try:
                                        pages = await context.pages
                                        print(f"   📄 Existing pages: {len(pages)}")
                                        
                                        if pages:
                                            page = pages[0]
                                            print(f"   📍 Current URL: {page.url}")
                                        else:
                                            print("   📄 No existing pages, creating new one...")
                                            page = await context.new_page()
                                            print("   ✅ New page created successfully")
                                            
                                    except Exception as page_error:
                                        print(f"   ❌ Page test failed: {page_error}")
                                        
                                else:
                                    print("   ❌ No browser context in browser_info")
                                    
                            else:
                                print(f"   ❌ Browser profile ID not found in active_browsers")
                                print(f"       Looking for: {browser_profile_id}")
                                print(f"       Available: {list(profile_manager.active_browsers.keys())}")
                                
                        else:
                            print(f"   ❌ open_facebook_login failed: {open_result.get('message')}")
                            
                    except Exception as pm_error:
                        print(f"   ❌ ProfileManager call failed: {pm_error}")
                        logger.exception("ProfileManager error details:")
                        
                else:
                    print("❌ No profiles found in database")
                    print("   Create a profile first via web interface")
                    return False
                    
        except Exception as db_error:
            print(f"❌ Database error: {db_error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Debug test failed: {e}")
        logger.exception("Debug error details:")
        return False

async def test_fallback_creation():
    """Test fallback browser creation"""
    
    print(f"\n🔄 Fallback Browser Creation Test")
    print("=" * 40)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if profiles:
                test_profile = profiles[0]
                
                # Test fallback creation
                scraper = FacebookScraper()
                
                print(f"🔧 Testing fallback session creation...")
                crawler = await scraper._create_fallback_session(test_profile, "fallback_test")
                
                if crawler:
                    print("✅ Fallback browser created successfully!")
                    print("🖥️  Browser window should be visible")
                    
                    # Test page access
                    page = crawler.crawler_strategy.page
                    if page:
                        print("✅ Page object accessible")
                        
                        # Test navigation
                        print("🌐 Testing navigation...")
                        await page.goto("https://www.google.com")
                        title = await page.title()
                        print(f"📄 Page title: {title}")
                        
                        input("Press Enter to close browser...")
                        
                        # Cleanup
                        await scraper.cleanup_session("fallback_test")
                        print("✅ Cleanup completed")
                        
                    else:
                        print("❌ No page object available")
                        
                else:
                    print("❌ Fallback browser creation failed")
                    
            else:
                print("❌ No profiles available for testing")
                
        return True
        
    except Exception as e:
        print(f"❌ Fallback test failed: {e}")
        logger.exception("Fallback test error:")
        return False

async def main():
    """Main debug function"""
    
    print("🔍 ProfileManager Integration Debug")
    print("=" * 50)
    
    # Test ProfileManager
    pm_success = await debug_profile_manager()
    
    if not pm_success:
        print("\n⚠️  ProfileManager test failed, trying fallback...")
        
        # Test fallback
        fallback_success = await test_fallback_creation()
        
        if fallback_success:
            print("\n✅ Fallback method works!")
            print("📝 Recommendation: Use fallback approach for now")
        else:
            print("\n❌ Both ProfileManager and fallback failed")
    else:
        print("\n🎉 ProfileManager integration works!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Debug interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
