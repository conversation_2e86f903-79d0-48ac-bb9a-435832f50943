/**
 * Facebook Automation Desktop - Main React App Component
 */

import React from 'react';
import { ConfigProvider } from 'antd';

// Import the modern layout
import ModernLayout from './components/ModernLayout';

// Import global styles
import './styles/App.css';

// Ant Design theme configuration
const themeConfig = {
  token: {
    colorPrimary: '#667eea',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 8,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Card: {
      borderRadius: 16,
    },
    Menu: {
      borderRadius: 8,
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={themeConfig}>
      <div className="App">
        <ModernLayout />
      </div>
    </ConfigProvider>
  );
}

export default App;
