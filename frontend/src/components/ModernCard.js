/**
 * Modern Card Component - Reusable Card with Beautiful Design
 */

import React from 'react';
import { Card } from 'antd';

const ModernCard = ({ 
  title, 
  children, 
  extra, 
  gradient = false,
  gradientColors = ['#667eea', '#764ba2'],
  className = '',
  style = {},
  ...props 
}) => {
  const cardStyle = {
    borderRadius: '16px',
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: 'none',
    overflow: 'hidden',
    transition: 'all 0.3s ease',
    ...style
  };

  const headerStyle = gradient ? {
    background: `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,
    border: 'none',
    borderRadius: '16px 16px 0 0'
  } : {};

  const titleStyle = gradient ? {
    color: 'white',
    fontWeight: 600
  } : {};

  return (
    <Card
      title={title}
      extra={extra}
      className={`modern-card ${className}`}
      style={cardStyle}
      headStyle={headerStyle}
      {...props}
    >
      {gradient && (
        <style jsx>{`
          .modern-card .ant-card-head-title {
            color: white !important;
            font-weight: 600 !important;
          }
          .modern-card .ant-card-extra {
            color: white !important;
          }
          .modern-card .ant-card-extra .ant-btn {
            color: white !important;
            border-color: rgba(255, 255, 255, 0.3) !important;
            background: rgba(255, 255, 255, 0.1) !important;
          }
          .modern-card .ant-card-extra .ant-btn:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
          }
        `}</style>
      )}
      {children}
    </Card>
  );
};

export default ModernCard;
