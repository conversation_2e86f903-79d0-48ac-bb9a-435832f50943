/**
 * Modern Layout Component - Complete Desktop Layout
 */

import React, { useState, useEffect } from 'react';
import { Layout, Spin, notification, BackTop } from 'antd';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { UpOutlined } from '@ant-design/icons';

// Import components
import ModernSidebar from './ModernSidebar';
import ModernHeader from './ModernHeader';
import ModernDashboard from './ModernDashboard';

// Import pages
import ProfileManager from '../pages/ProfileManager';
import Scraping from '../pages/Scraping';
import Messaging from '../pages/Messaging';
import Settings from '../pages/Settings';

// Import services
import { apiService } from '../services/api';

const { Content } = Layout;

const ModernLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [backendStatus, setBackendStatus] = useState('connecting');

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      
      // Check backend connection
      await checkBackendStatus();
      
      // Initialize app data
      await loadInitialData();
      
      // Show welcome notification
      notification.success({
        message: 'Welcome to Facebook Automation Desktop',
        description: 'All systems are ready. You can start automating your Facebook tasks.',
        duration: 4,
        placement: 'topRight'
      });
      
    } catch (error) {
      console.error('App initialization failed:', error);
      notification.error({
        message: 'Initialization Failed',
        description: 'Failed to initialize the application. Please check your backend connection.',
        duration: 6,
        placement: 'topRight'
      });
    } finally {
      setLoading(false);
    }
  };

  const checkBackendStatus = async () => {
    try {
      const response = await apiService.get('/health');
      
      if (response.status === 'healthy') {
        setBackendStatus('connected');
      } else {
        setBackendStatus('error');
      }
    } catch (error) {
      console.error('Backend connection failed:', error);
      setBackendStatus('error');
      throw error;
    }
  };

  const loadInitialData = async () => {
    try {
      // Pre-load essential data
      await Promise.all([
        apiService.getProfiles().catch(() => []),
        apiService.get('/api/system/stats').catch(() => ({}))
      ]);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  };

  // Loading screen
  if (loading) {
    return (
      <div className="app-loading">
        <div className="loading-content">
          <div className="loading-logo">📱</div>
          <h2>Facebook Automation Desktop</h2>
          <Spin size="large" />
          <p>Initializing application...</p>
          <div className="loading-progress">
            <div className="progress-bar"></div>
          </div>
        </div>
        
        <style jsx>{`
          .app-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
          }

          .loading-content {
            text-align: center;
            color: white;
          }

          .loading-logo {
            font-size: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
          }

          .loading-content h2 {
            color: white;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 300;
          }

          .loading-content p {
            color: rgba(255, 255, 255, 0.8);
            margin: 20px 0;
            font-size: 16px;
          }

          .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 20px auto;
            overflow: hidden;
          }

          .progress-bar {
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            animation: loading 2s infinite;
          }

          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
          }

          @keyframes loading {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <Router>
      <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
        {/* Sidebar */}
        <ModernSidebar collapsed={collapsed} setCollapsed={setCollapsed} />
        
        {/* Main Layout */}
        <Layout style={{ 
          marginLeft: collapsed ? 80 : 280,
          transition: 'all 0.3s ease',
          minHeight: '100vh'
        }}>
          {/* Header */}
          <ModernHeader collapsed={collapsed} setCollapsed={setCollapsed} />
          
          {/* Content */}
          <Content
            style={{
              marginTop: 80, // Header height + padding
              padding: '24px',
              minHeight: 'calc(100vh - 80px)',
              background: '#f5f5f5',
              overflow: 'auto'
            }}
          >
            <div className="content-wrapper" style={{
              maxWidth: '1400px',
              margin: '0 auto',
              animation: 'fadeIn 0.5s ease-in'
            }}>
              <Routes>
                <Route path="/" element={<ModernDashboard />} />
                <Route path="/profiles" element={<ProfileManager />} />
                <Route path="/scraping" element={<Scraping />} />
                <Route path="/messaging" element={<Messaging />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </div>
          </Content>
        </Layout>

        {/* Back to Top Button */}
        <BackTop
          style={{
            right: '30px',
            bottom: '30px'
          }}
        >
          <div style={{
            height: '50px',
            width: '50px',
            lineHeight: '50px',
            borderRadius: '25px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            textAlign: 'center',
            fontSize: '18px',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)',
            transition: 'all 0.3s ease'
          }}>
            <UpOutlined />
          </div>
        </BackTop>

        {/* Global Styles */}
        <style jsx global>{`
          /* Fade in animation */
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
          }

          /* Custom scrollbar */
          ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }

          ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
          }

          ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
          }

          ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }

          /* Card hover effects */
          .custom-card {
            border-radius: 16px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
            border: none !important;
            overflow: hidden !important;
            transition: all 0.3s ease !important;
          }

          .custom-card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
            transform: translateY(-2px) !important;
          }

          .custom-card .ant-card-head {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border-bottom: none !important;
            border-radius: 16px 16px 0 0 !important;
          }

          .custom-card .ant-card-head-title {
            color: white !important;
            font-weight: 600 !important;
          }

          /* Button styles */
          .gradient-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 12px !important;
            color: white !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
          }

          .gradient-button:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
          }

          /* Progress bars */
          .custom-progress .ant-progress-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          }

          /* Tables */
          .custom-table .ant-table-thead > tr > th {
            background: #fafafa !important;
            border-bottom: 2px solid #f0f0f0 !important;
            font-weight: 600 !important;
            color: #262626 !important;
          }

          .custom-table .ant-table-tbody > tr:hover > td {
            background: #f5f5f5 !important;
          }

          /* Forms */
          .custom-form .ant-form-item-label > label {
            font-weight: 500 !important;
            color: #262626 !important;
          }

          .custom-form .ant-input,
          .custom-form .ant-select-selector {
            border-radius: 8px !important;
            border: 1px solid #d9d9d9 !important;
            transition: all 0.3s ease !important;
          }

          .custom-form .ant-input:focus,
          .custom-form .ant-select-focused .ant-select-selector {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
          }

          /* Notifications */
          .ant-notification {
            border-radius: 12px !important;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
          }

          /* Animation classes */
          .fade-in {
            animation: fadeIn 0.5s ease-in;
          }

          .slide-up {
            animation: slideUp 0.5s ease-out;
          }

          @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
          }

          .bounce-in {
            animation: bounceIn 0.6s ease-out;
          }

          @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
          }

          /* Responsive design */
          @media (max-width: 768px) {
            .content-wrapper {
              padding: 0 8px !important;
            }
            
            .custom-card {
              margin-bottom: 16px !important;
            }
          }
        `}</style>
      </Layout>
    </Router>
  );
};

export default ModernLayout;
