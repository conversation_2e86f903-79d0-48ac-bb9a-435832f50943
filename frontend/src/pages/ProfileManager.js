/**
 * Profile Manager Page Component
 */

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, Select, Card, Tag, 
  message, Popconfirm, Tooltip, Row, Col, InputNumber
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined,
  StopOutlined, LoginOutlined, EyeOutlined, ReloadOutlined,
  ChromeOutlined, FacebookOutlined, CheckCircleOutlined, CloseOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;

const ProfileManager = () => {
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProfile, setEditingProfile] = useState(null);
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [form] = Form.useForm();
  const [loginForm] = Form.useForm();

  useEffect(() => {
    loadProfiles();
  }, []);

  const loadProfiles = async () => {
    try {
      setLoading(true);
      const data = await apiService.getProfiles();
      setProfiles(data);
    } catch (error) {
      message.error('Failed to load profiles: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProfile = () => {
    setEditingProfile(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEditProfile = (profile) => {
    setEditingProfile(profile);
    setModalVisible(true);
    form.setFieldsValue({
      name: profile.name,
      proxy_type: profile.proxy_config.type,
      proxy_host: profile.proxy_config.host,
      proxy_port: profile.proxy_config.port,
      proxy_username: profile.proxy_config.username,
      proxy_password: profile.proxy_config.password
    });
  };

  const handleSubmit = async (values) => {
    try {
      const profileData = {
        name: values.name,
        proxy_config: {
          type: values.proxy_type || 'no_proxy',
          host: values.proxy_host,
          port: values.proxy_port,
          username: values.proxy_username,
          password: values.proxy_password
        }
      };

      if (editingProfile) {
        await apiService.updateProfile(editingProfile.id, profileData);
        message.success('Profile updated successfully');
      } else {
        await apiService.createProfile(profileData);
        message.success('Profile created successfully');
      }

      setModalVisible(false);
      loadProfiles();
    } catch (error) {
      message.error('Failed to save profile: ' + error.message);
    }
  };

  const handleDeleteProfile = async (profileId) => {
    try {
      await apiService.deleteProfile(profileId);
      message.success('Profile deleted successfully');
      loadProfiles();
    } catch (error) {
      message.error('Failed to delete profile: ' + error.message);
    }
  };

  const handleTestProfile = async (profile) => {
    try {
      message.loading('Testing profile...', 0);
      const result = await apiService.testProfile(profile.id);
      message.destroy();
      
      if (result.success) {
        message.success(`Profile test successful! IP: ${result.ip_address}`);
      } else {
        message.error(`Profile test failed: ${result.message}`);
      }
      
      loadProfiles();
    } catch (error) {
      message.destroy();
      message.error('Failed to test profile: ' + error.message);
    }
  };

  const handleLoginFacebook = (profile) => {
    setSelectedProfile(profile);
    setLoginModalVisible(true);
    loginForm.resetFields();
  };

  const handleLaunchBrowser = async (profile) => {
    try {
      setLoading(true);

      // First check browser status
      const statusResult = await apiService.getBrowserStatus(profile.id);
      if (statusResult.success && statusResult.browser_active) {
        message.info('Browser is already launched for this profile');
        return;
      }

      const result = await apiService.launchBrowser(profile.id, false);
      if (result.success) {
        if (result.already_active) {
          message.info('Browser is already launched for this profile');
        } else {
          message.success('Browser launched successfully');
        }
        loadProfiles(); // Refresh profiles
      } else {
        message.error(result.message || 'Failed to launch browser');
      }
    } catch (error) {
      message.error('Failed to launch browser: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenFacebook = async (profile) => {
    try {
      setLoading(true);
      const result = await apiService.openFacebook(profile.id);
      if (result.success) {
        message.success('Facebook login page opened. Please complete login manually.');
      } else {
        message.error(result.message || 'Failed to open Facebook');
      }
    } catch (error) {
      message.error('Failed to open Facebook: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteFacebookLogin = async (profile) => {
    try {
      setLoading(true);
      const result = await apiService.completeFacebookLogin(profile.id);
      if (result.success && result.logged_in) {
        message.success('Facebook login completed successfully!');
        loadProfiles(); // Refresh profiles
      } else {
        message.warning(result.message || 'Login not completed. Please complete the login process.');
      }
    } catch (error) {
      message.error('Failed to complete Facebook login: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseBrowser = async (profile) => {
    try {
      setLoading(true);
      const result = await apiService.closeBrowser(profile.id);
      if (result.success) {
        message.success('Browser closed successfully');
        loadProfiles(); // Refresh profiles
      } else {
        message.error(result.message || 'Failed to close browser');
      }
    } catch (error) {
      message.error('Failed to close browser: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLoginSubmit = async (values) => {
    try {
      const result = await apiService.loginFacebook(selectedProfile.id, values);
      
      if (result.success) {
        if (result.manual_login_required) {
          message.info('Facebook login page opened. Please login manually in the browser.');
        } else {
          message.success('Facebook login successful');
        }
      } else {
        message.error(`Facebook login failed: ${result.message}`);
      }
      
      setLoginModalVisible(false);
      loadProfiles();
    } catch (error) {
      message.error('Failed to login Facebook: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      created: { color: 'default', text: 'Created' },
      active: { color: 'processing', text: 'Active' },
      logged_in: { color: 'success', text: 'Logged In' },
      error: { color: 'error', text: 'Error' },
      disabled: { color: 'default', text: 'Disabled' }
    };
    
    const config = statusConfig[status] || statusConfig.created;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name)
    },
    {
      title: 'Proxy',
      key: 'proxy',
      render: (_, record) => {
        const proxy = record.proxy_config;
        if (proxy.type === 'no_proxy') {
          return <Tag color="default">No Proxy</Tag>;
        }
        return (
          <Tooltip title={`${proxy.host}:${proxy.port}`}>
            <Tag color="blue">{proxy.type.toUpperCase()}</Tag>
          </Tooltip>
        );
      }
    },
    {
      title: 'Facebook Status',
      key: 'facebook_status',
      render: (_, record) => (
        record.facebook_logged_in ? 
          <Tag color="success">Logged In ({record.facebook_username})</Tag> :
          <Tag color="default">Not Logged In</Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
      filters: [
        { text: 'Created', value: 'created' },
        { text: 'Active', value: 'active' },
        { text: 'Logged In', value: 'logged_in' },
        { text: 'Error', value: 'error' }
      ],
      onFilter: (value, record) => record.status === value
    },
    {
      title: 'Last Used',
      dataIndex: 'last_used',
      key: 'last_used',
      render: (date) => date ? new Date(date).toLocaleString() : 'Never',
      sorter: (a, b) => new Date(a.last_used || 0) - new Date(b.last_used || 0)
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small" wrap>
          <Tooltip title="Launch Browser">
            <Button
              icon={<ChromeOutlined />}
              size="small"
              type="primary"
              onClick={() => handleLaunchBrowser(record)}
            />
          </Tooltip>

          <Tooltip title="Open Facebook">
            <Button
              icon={<FacebookOutlined />}
              size="small"
              onClick={() => handleOpenFacebook(record)}
            />
          </Tooltip>

          <Tooltip title="Complete Facebook Login">
            <Button
              icon={<CheckCircleOutlined />}
              size="small"
              type="primary"
              onClick={() => handleCompleteFacebookLogin(record)}
            />
          </Tooltip>

          <Tooltip title="Close Browser">
            <Button
              icon={<CloseOutlined />}
              size="small"
              onClick={() => handleCloseBrowser(record)}
            />
          </Tooltip>

          <Tooltip title="Test Profile">
            <Button
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => handleTestProfile(record)}
            />
          </Tooltip>

          <Tooltip title="Edit Profile">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditProfile(record)}
            />
          </Tooltip>

          <Tooltip title="Delete Profile">
            <Popconfirm
              title="Are you sure you want to delete this profile?"
              onConfirm={() => handleDeleteProfile(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Profile Manager</h1>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadProfiles}
              loading={loading}
            >
              Refresh
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleCreateProfile}
            >
              Create Profile
            </Button>
          </Space>
        </Col>
      </Row>

      <Card>
        <Table
          columns={columns}
          dataSource={profiles}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} profiles`
          }}
        />
      </Card>

      {/* Create/Edit Profile Modal */}
      <Modal
        title={editingProfile ? 'Edit Profile' : 'Create Profile'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Profile Name"
            rules={[{ required: true, message: 'Please enter profile name' }]}
          >
            <Input placeholder="Enter profile name" />
          </Form.Item>

          <Card title="Proxy Settings" size="small" style={{ marginBottom: 16 }}>
            <Form.Item
              name="proxy_type"
              label="Proxy Type"
              initialValue="no_proxy"
            >
              <Select>
                <Option value="no_proxy">No Proxy (Local Network)</Option>
                <Option value="http">HTTP</Option>
                <Option value="https">HTTPS</Option>
                <Option value="socks5">SOCKS5</Option>
                <Option value="ssh">SSH</Option>
              </Select>
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.proxy_type !== currentValues.proxy_type
              }
            >
              {({ getFieldValue }) => {
                const proxyType = getFieldValue('proxy_type');
                if (proxyType === 'no_proxy') return null;

                return (
                  <>
                    <Row gutter={16}>
                      <Col span={16}>
                        <Form.Item
                          name="proxy_host"
                          label="Host"
                          rules={[{ required: true, message: 'Please enter proxy host' }]}
                        >
                          <Input placeholder="proxy.example.com" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="proxy_port"
                          label="Port"
                          rules={[{ required: true, message: 'Please enter proxy port' }]}
                        >
                          <InputNumber 
                            placeholder="8080" 
                            min={1} 
                            max={65535} 
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="proxy_username"
                          label="Username"
                        >
                          <Input placeholder="Optional" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="proxy_password"
                          label="Password"
                        >
                          <Input.Password placeholder="Optional" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </>
                );
              }}
            </Form.Item>
          </Card>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingProfile ? 'Update' : 'Create'} Profile
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Facebook Login Modal */}
      <Modal
        title="Facebook Login"
        open={loginModalVisible}
        onCancel={() => setLoginModalVisible(false)}
        footer={null}
      >
        <Form
          form={loginForm}
          layout="vertical"
          onFinish={handleLoginSubmit}
        >
          <Form.Item
            name="username"
            label="Facebook Username/Email"
            rules={[{ required: true, message: 'Please enter Facebook username' }]}
          >
            <Input placeholder="Enter Facebook username or email" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Facebook Password"
            rules={[{ required: true, message: 'Please enter Facebook password' }]}
          >
            <Input.Password placeholder="Enter Facebook password" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Login
              </Button>
              <Button onClick={() => setLoginModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProfileManager;
