/**
 * API Service for communicating with the backend
 */

import axios from 'axios';

class ApiService {
  constructor() {
    this.baseURL = null;
    this.client = null;
    this.init();
  }

  async init() {
    try {
      // Check if running in Electron or web browser
      if (window.electronAPI) {
        // Electron environment
        this.baseURL = await window.electronAPI.getBackendUrl();
      } else {
        // Web browser environment
        this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
      }

      // Create axios instance
      this.client = axios.create({
        baseURL: this.baseURL,
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Request interceptor
      this.client.interceptors.request.use(
        (config) => {
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
          return config;
        },
        (error) => {
          console.error('API Request Error:', error);
          return Promise.reject(error);
        }
      );

      // Response interceptor
      this.client.interceptors.response.use(
        (response) => {
          console.log(`API Response: ${response.status} ${response.config.url}`);
          return response.data;
        },
        (error) => {
          console.error('API Response Error:', error);
          
          // Handle common errors
          if (error.response) {
            // Server responded with error status
            const { status, data } = error.response;
            throw new Error(data.detail || data.message || `HTTP ${status} Error`);
          } else if (error.request) {
            // Request was made but no response received
            throw new Error('No response from server. Please check if the backend is running.');
          } else {
            // Something else happened
            throw new Error(error.message || 'Unknown error occurred');
          }
        }
      );

    } catch (error) {
      console.error('Failed to initialize API service:', error);
      throw error;
    }
  }

  // Generic HTTP methods
  async get(url, config = {}) {
    return this.client.get(url, config);
  }

  async post(url, data = {}, config = {}) {
    return this.client.post(url, data, config);
  }

  async put(url, data = {}, config = {}) {
    return this.client.put(url, data, config);
  }

  async delete(url, config = {}) {
    return this.client.delete(url, config);
  }

  // Profile Management APIs
  async getProfiles() {
    return this.get('/api/profiles/');
  }

  async createProfile(profileData) {
    return this.post('/api/profiles/', profileData);
  }

  async updateProfile(profileId, profileData) {
    return this.put(`/api/profiles/${profileId}`, profileData);
  }

  async deleteProfile(profileId) {
    return this.delete(`/api/profiles/${profileId}`);
  }

  async testProfile(profileId) {
    return this.post(`/api/profiles/${profileId}/test`);
  }

  async loginFacebook(profileId, credentials) {
    return this.post(`/api/profiles/${profileId}/login`, credentials);
  }

  async launchBrowser(profileId, headless = false) {
    return this.post(`/api/profiles/${profileId}/launch-browser?headless=${headless}`);
  }

  async openFacebook(profileId) {
    return this.post(`/api/profiles/${profileId}/open-facebook`);
  }

  async completeFacebookLogin(profileId) {
    return this.post(`/api/profiles/${profileId}/complete-login`);
  }

  async getBrowserStatus(profileId) {
    return this.get(`/api/profiles/${profileId}/browser-status`);
  }

  async closeBrowser(profileId) {
    return this.post(`/api/profiles/${profileId}/close-browser`);
  }

  // Scraping APIs
  async startScraping(scrapingConfig) {
    return this.post('/api/scraping/start', scrapingConfig);
  }

  async getScrapingStatus(taskId) {
    return this.get(`/api/scraping/status/${taskId}`);
  }

  async stopScraping(taskId) {
    return this.post(`/api/scraping/stop/${taskId}`);
  }

  async getScrapingResults(taskId) {
    return this.get(`/api/scraping/results/${taskId}`);
  }

  async exportScrapingResults(taskId, format = 'excel') {
    return this.get(`/api/scraping/export/${taskId}?format=${format}`, {
      responseType: 'blob'
    });
  }

  // Messaging APIs
  async startMessaging(messagingConfig) {
    return this.post('/api/messaging/start', messagingConfig);
  }

  async getMessagingStatus(taskId) {
    return this.get(`/api/messaging/status/${taskId}`);
  }

  async stopMessaging(taskId) {
    return this.post(`/api/messaging/stop/${taskId}`);
  }

  async getMessagingResults(taskId) {
    return this.get(`/api/messaging/results/${taskId}`);
  }

  async uploadRecipientList(file) {
    const formData = new FormData();
    formData.append('file', file);

    return this.post('/api/messaging/upload-recipients', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // System APIs
  async getSystemStatus() {
    return this.get('/health');
  }

  async getSystemStats() {
    return this.get('/api/system/stats');
  }
}

// Create and export singleton instance
export const apiService = new ApiService();
