#!/bin/bash

# Build script for local development
# Facebook Automation Desktop Application

set -e  # Exit on any error

echo "🔨 Building Facebook Automation Desktop Application for local development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Clean previous builds
clean_builds() {
    print_status "Cleaning previous builds..."
    
    # Clean frontend build
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        print_status "Cleaned frontend dist directory"
    fi
    
    # Clean backend build
    if [ -d "backend/dist" ]; then
        rm -rf backend/dist
        print_status "Cleaned backend dist directory"
    fi
    
    # Clean electron build
    if [ -d "frontend/build" ]; then
        rm -rf frontend/build
        print_status "Cleaned electron build directory"
    fi
    
    print_success "Build directories cleaned"
}

# Get project directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# Build frontend
build_frontend() {
    print_status "Building frontend..."

    cd "$PROJECT_DIR/frontend"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Build webpack bundle
    print_status "Building webpack bundle..."
    npm run webpack:build
    
    print_success "Frontend build completed"

    cd "$PROJECT_DIR"
}

# Build backend
build_backend() {
    print_status "Building backend..."

    cd "$PROJECT_DIR/backend"
    
    # Activate virtual environment
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found. Please run setup-local.sh first."
        exit 1
    fi
    
    source venv/bin/activate
    
    # Install dependencies if needed
    print_status "Checking backend dependencies..."
    pip install -r requirements.txt --quiet
    
    # Run tests (optional)
    if [ "$1" = "--with-tests" ]; then
        print_status "Running backend tests..."
        if [ -f "pytest.ini" ] || [ -d "tests" ]; then
            python -m pytest tests/ -v || print_warning "Some tests failed"
        else
            print_warning "No tests found to run"
        fi
    fi
    
    # Create standalone executable
    print_status "Creating backend executable..."
    
    # Install PyInstaller if not present
    pip install pyinstaller --quiet
    
    # Build executable
    pyinstaller --onefile \
                --name facebook-automation-backend \
                --distpath dist \
                --workpath build \
                --specpath . \
                main.py
    
    print_success "Backend build completed"

    cd "$PROJECT_DIR"
}

# Package Electron app
package_electron() {
    print_status "Packaging Electron application..."

    cd "$PROJECT_DIR/frontend"
    
    # Build Electron app
    print_status "Building Electron package..."
    npm run package
    
    print_success "Electron application packaged"

    cd "$PROJECT_DIR"
}

# Verify builds
verify_builds() {
    print_status "Verifying builds..."
    
    # Check frontend build
    if [ -d "$PROJECT_DIR/frontend/dist" ] && [ "$(ls -A "$PROJECT_DIR/frontend/dist")" ]; then
        print_success "Frontend build verified"
    else
        print_error "Frontend build failed or empty"
        return 1
    fi
    
    # Check backend build
    if [ -f "$PROJECT_DIR/backend/dist/facebook-automation-backend" ]; then
        print_success "Backend build verified"
    else
        print_error "Backend build failed"
        return 1
    fi
    
    # Check Electron build
    if [ -d "$PROJECT_DIR/frontend/dist" ] && [ "$(find "$PROJECT_DIR/frontend/dist" -name '*.app' -o -name '*.exe' -o -name '*.AppImage' 2>/dev/null)" ]; then
        print_success "Electron package verified"
    else
        print_warning "Electron package not found (this is normal for development builds)"
    fi
    
    print_success "Build verification completed"
}

# Show build info
show_build_info() {
    print_status "Build Information:"
    echo ""
    
    # Frontend build info
    if [ -d "$PROJECT_DIR/frontend/dist" ]; then
        FRONTEND_SIZE=$(du -sh "$PROJECT_DIR/frontend/dist" | cut -f1)
        echo "  📦 Frontend build: frontend/dist/ ($FRONTEND_SIZE)"
    fi

    # Backend build info
    if [ -f "$PROJECT_DIR/backend/dist/facebook-automation-backend" ]; then
        BACKEND_SIZE=$(du -sh "$PROJECT_DIR/backend/dist/facebook-automation-backend" | cut -f1)
        echo "  🐍 Backend executable: backend/dist/facebook-automation-backend ($BACKEND_SIZE)"
    fi

    # Electron build info
    if [ -d "$PROJECT_DIR/frontend/dist" ]; then
        ELECTRON_APPS=$(find "$PROJECT_DIR/frontend/dist" -name '*.app' -o -name '*.exe' -o -name '*.AppImage' 2>/dev/null)
        if [ -n "$ELECTRON_APPS" ]; then
            echo "  ⚡ Electron packages:"
            echo "$ELECTRON_APPS" | while read app; do
                if [ -n "$app" ]; then
                    APP_SIZE=$(du -sh "$app" | cut -f1)
                    echo "    - $app ($APP_SIZE)"
                fi
            done
        fi
    fi
    
    echo ""
}

# Main build function
main() {
    print_status "Starting local build process..."
    
    # Parse arguments
    WITH_TESTS=false
    CLEAN_FIRST=false
    PACKAGE_ELECTRON=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --with-tests)
                WITH_TESTS=true
                shift
                ;;
            --clean)
                CLEAN_FIRST=true
                shift
                ;;
            --package)
                PACKAGE_ELECTRON=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --clean         Clean previous builds before building"
                echo "  --with-tests    Run tests during backend build"
                echo "  --package       Package Electron application"
                echo "  --help          Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done
    
    # Clean if requested
    if [ "$CLEAN_FIRST" = true ]; then
        clean_builds
    fi
    
    # Build components
    build_frontend
    
    if [ "$WITH_TESTS" = true ]; then
        build_backend --with-tests
    else
        build_backend
    fi
    
    # Package Electron if requested
    if [ "$PACKAGE_ELECTRON" = true ]; then
        package_electron
    fi
    
    # Verify builds
    verify_builds
    
    # Show build information
    show_build_info
    
    print_success "🎉 Local build completed successfully!"
    echo ""
    print_status "To run the application:"
    echo "  1. Quick start: ./scripts/dev.sh"
    echo "  2. Full featured: ./scripts/run-local.sh"
    echo "  3. Manual backend: ./backend/dist/facebook-automation-backend"
    echo "  4. Manual frontend: cd frontend && npm start"
    echo ""
}

# Run main function
main "$@"
