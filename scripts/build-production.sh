#!/bin/bash

# Build script for production deployment
# Facebook Automation Desktop Application

set -e  # Exit on any error

echo "🏭 Building Facebook Automation Desktop Application for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BUILD_DIR="build"
DIST_DIR="dist"
VERSION=$(date +%Y%m%d-%H%M%S)

# Clean previous builds
clean_builds() {
    print_status "Cleaning previous builds..."
    
    rm -rf "$BUILD_DIR"
    rm -rf "$DIST_DIR"
    
    # Clean component builds
    rm -rf frontend/dist
    rm -rf frontend/build
    rm -rf backend/dist
    rm -rf backend/build
    
    print_success "Build directories cleaned"
}

# Create build directories
create_build_dirs() {
    print_status "Creating build directories..."
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$DIST_DIR"
    mkdir -p "$BUILD_DIR/backend"
    mkdir -p "$BUILD_DIR/frontend"
    mkdir -p "$BUILD_DIR/scripts"
    
    print_success "Build directories created"
}

# Build frontend for production
build_frontend() {
    print_status "Building frontend for production..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing frontend dependencies..."
    npm ci --production=false
    
    # Create production environment
    cat > .env.production << EOF
REACT_APP_API_URL=/api
REACT_APP_WS_URL=/ws
NODE_ENV=production
GENERATE_SOURCEMAP=false
EOF
    
    # Build for production
    print_status "Building React application..."
    npm run webpack:build
    
    # Copy built files
    cp -r dist "../$BUILD_DIR/frontend/"
    cp package.json "../$BUILD_DIR/frontend/"
    cp -r public "../$BUILD_DIR/frontend/" 2>/dev/null || true
    
    print_success "Frontend build completed"
    
    cd ..
}

# Build backend for production
build_backend() {
    print_status "Building backend for production..."
    
    cd backend
    
    # Create virtual environment
    python3 -m venv build_venv
    source build_venv/bin/activate
    
    # Install dependencies
    print_status "Installing backend dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    pip install gunicorn
    
    # Run tests
    if [ "$1" = "--with-tests" ]; then
        print_status "Running backend tests..."
        if [ -d "tests" ]; then
            python -m pytest tests/ -v --tb=short
        else
            print_warning "No tests found"
        fi
    fi
    
    # Create production requirements
    pip freeze > requirements-prod.txt
    
    # Copy application files
    cp -r app "../$BUILD_DIR/backend/"
    cp main.py "../$BUILD_DIR/backend/"
    cp requirements-prod.txt "../$BUILD_DIR/backend/requirements.txt"
    cp alembic.ini "../$BUILD_DIR/backend/" 2>/dev/null || true
    cp -r alembic "../$BUILD_DIR/backend/" 2>/dev/null || true
    
    # Create production startup script
    cat > "../$BUILD_DIR/backend/start.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
exec gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
EOF
    chmod +x "../$BUILD_DIR/backend/start.sh"
    
    # Create Celery startup script
    cat > "../$BUILD_DIR/backend/start-worker.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
exec celery -A app.celery worker --loglevel=info
EOF
    chmod +x "../$BUILD_DIR/backend/start-worker.sh"
    
    print_success "Backend build completed"
    
    cd ..
}

# Build Docker images
build_docker() {
    print_status "Building Docker images..."
    
    # Create Dockerfile for backend
    cat > "$BUILD_DIR/backend/Dockerfile" << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000"]
EOF
    
    # Create Dockerfile for frontend
    cat > "$BUILD_DIR/frontend/Dockerfile" << 'EOF'
FROM nginx:alpine

# Copy built frontend
COPY dist/ /usr/share/nginx/html/

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
EOF
    
    # Create nginx configuration for frontend
    cat > "$BUILD_DIR/frontend/nginx.conf" << 'EOF'
server {
    listen 80;
    server_name localhost;
    
    root /usr/share/nginx/html;
    index index.html;
    
    # Handle React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy (when used with backend container)
    location /api/ {
        proxy_pass http://backend:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket proxy
    location /ws/ {
        proxy_pass http://backend:8000/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
EOF
    
    # Create docker-compose for production
    cat > "$BUILD_DIR/docker-compose.prod.yml" << 'EOF'
version: '3.8'

services:
  backend:
    build: ./backend
    environment:
      - DATABASE_URL=******************************************/facebook_automation
      - REDIS_URL=redis://redis:6379
      - DEBUG=false
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data
    
  worker:
    build: ./backend
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=******************************************/facebook_automation
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data
    
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=facebook_automation
      - POSTGRES_USER=fbautomation
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
EOF
    
    print_success "Docker configuration created"
}

# Copy scripts and documentation
copy_scripts() {
    print_status "Copying scripts and documentation..."
    
    # Copy setup scripts
    cp scripts/setup-production.sh "$BUILD_DIR/scripts/"
    cp scripts/setup-local.sh "$BUILD_DIR/scripts/"
    cp scripts/setup-local.bat "$BUILD_DIR/scripts/"
    
    # Create deployment guide
    cat > "$BUILD_DIR/DEPLOYMENT.md" << 'EOF'
# Production Deployment Guide

## Prerequisites
- Ubuntu 20.04+ or CentOS 8+
- Root access
- Internet connection

## Quick Deployment

### Option 1: Traditional Deployment
```bash
sudo ./scripts/setup-production.sh
```

### Option 2: Docker Deployment
```bash
# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Deploy application
docker-compose -f docker-compose.prod.yml up -d
```

## Configuration
- Backend config: `backend/.env`
- Frontend config: `frontend/.env`
- Database: PostgreSQL
- Cache: Redis
- Web server: Nginx

## Monitoring
- Logs: `/var/log/facebook-automation/`
- Status: `systemctl status facebook-automation-*`

## Security Notes
- Change default passwords
- Configure SSL/TLS
- Set up firewall rules
- Regular security updates
EOF
    
    # Create README
    cat > "$BUILD_DIR/README.md" << 'EOF'
# Facebook Automation Desktop Application - Production Build

This is a production-ready build of the Facebook Automation Desktop Application.

## Contents
- `backend/` - FastAPI backend application
- `frontend/` - React frontend application
- `scripts/` - Deployment and setup scripts
- `docker-compose.prod.yml` - Docker deployment configuration

## Quick Start
See `DEPLOYMENT.md` for detailed deployment instructions.

## Support
For issues and support, please refer to the project documentation.
EOF
    
    print_success "Scripts and documentation copied"
}

# Create distribution package
create_distribution() {
    print_status "Creating distribution package..."
    
    # Create version info
    cat > "$BUILD_DIR/VERSION" << EOF
Version: $VERSION
Build Date: $(date)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "unknown")
EOF
    
    # Create tarball
    tar -czf "$DIST_DIR/facebook-automation-$VERSION.tar.gz" -C "$BUILD_DIR" .
    
    # Create zip file
    cd "$BUILD_DIR"
    zip -r "../$DIST_DIR/facebook-automation-$VERSION.zip" .
    cd ..
    
    print_success "Distribution packages created"
}

# Verify build
verify_build() {
    print_status "Verifying build..."
    
    # Check required files
    REQUIRED_FILES=(
        "$BUILD_DIR/backend/main.py"
        "$BUILD_DIR/backend/requirements.txt"
        "$BUILD_DIR/frontend/dist/index.html"
        "$BUILD_DIR/scripts/setup-production.sh"
        "$BUILD_DIR/docker-compose.prod.yml"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Missing required file: $file"
            return 1
        fi
    done
    
    print_success "Build verification completed"
}

# Show build summary
show_build_summary() {
    print_status "Build Summary:"
    echo ""
    
    # Calculate sizes
    BUILD_SIZE=$(du -sh "$BUILD_DIR" | cut -f1)
    
    echo "  📦 Build directory: $BUILD_DIR ($BUILD_SIZE)"
    echo "  📋 Version: $VERSION"
    echo "  🗂️  Distribution packages:"
    
    if [ -f "$DIST_DIR/facebook-automation-$VERSION.tar.gz" ]; then
        TAR_SIZE=$(du -sh "$DIST_DIR/facebook-automation-$VERSION.tar.gz" | cut -f1)
        echo "    - facebook-automation-$VERSION.tar.gz ($TAR_SIZE)"
    fi
    
    if [ -f "$DIST_DIR/facebook-automation-$VERSION.zip" ]; then
        ZIP_SIZE=$(du -sh "$DIST_DIR/facebook-automation-$VERSION.zip" | cut -f1)
        echo "    - facebook-automation-$VERSION.zip ($ZIP_SIZE)"
    fi
    
    echo ""
    print_status "Deployment options:"
    echo "  1. Traditional: Extract and run ./scripts/setup-production.sh"
    echo "  2. Docker: docker-compose -f docker-compose.prod.yml up -d"
    echo ""
}

# Main build function
main() {
    print_status "Starting production build process..."
    
    # Parse arguments
    WITH_TESTS=false
    CLEAN_FIRST=true
    BUILD_DOCKER=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --with-tests)
                WITH_TESTS=true
                shift
                ;;
            --no-clean)
                CLEAN_FIRST=false
                shift
                ;;
            --no-docker)
                BUILD_DOCKER=false
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --with-tests    Run tests during build"
                echo "  --no-clean      Don't clean previous builds"
                echo "  --no-docker     Skip Docker configuration"
                echo "  --help          Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done
    
    # Execute build steps
    if [ "$CLEAN_FIRST" = true ]; then
        clean_builds
    fi
    
    create_build_dirs
    build_frontend
    
    if [ "$WITH_TESTS" = true ]; then
        build_backend --with-tests
    else
        build_backend
    fi
    
    if [ "$BUILD_DOCKER" = true ]; then
        build_docker
    fi
    
    copy_scripts
    create_distribution
    verify_build
    show_build_summary
    
    print_success "🎉 Production build completed successfully!"
}

# Run main function
main "$@"
