#!/bin/bash

# Demo script to showcase Facebook Automation Desktop Application scripts
# Shows how to use all the scripts step by step

echo "🎬 Facebook Automation Desktop Application - Script Demo"
echo "========================================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_step() { echo -e "\n${BLUE}📋 STEP: $1${NC}"; }
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Demo mode
DEMO_MODE=${1:-interactive}

# Wait function for demo
wait_for_user() {
    if [ "$DEMO_MODE" = "interactive" ]; then
        echo ""
        read -p "Press Enter to continue..."
        echo ""
    else
        sleep 2
    fi
}

# Show available scripts
show_scripts() {
    print_step "Available Scripts Overview"
    
    echo "📁 Setup Scripts:"
    echo "  ./scripts/setup-local.sh      - Setup development environment (Linux/macOS)"
    echo "  ./scripts/setup-local.bat     - Setup development environment (Windows)"
    echo "  ./scripts/setup-production.sh - Setup production server"
    echo ""
    
    echo "🏗️ Build Scripts:"
    echo "  ./scripts/build-local.sh      - Build for development"
    echo "  ./scripts/build-production.sh - Build for production"
    echo ""
    
    echo "🚀 Run Scripts:"
    echo "  ./scripts/run-local.sh        - Run application locally (full featured)"
    echo "  ./scripts/dev.sh              - Quick development start"
    echo ""
    
    echo "📦 Deploy Scripts:"
    echo "  ./scripts/deploy.sh           - Automated deployment"
    echo ""
    
    echo "🎯 Management Scripts:"
    echo "  ./scripts/manage.sh           - Unified management interface"
    echo "  ./scripts/make-executable.sh  - Make all scripts executable"
    echo "  ./scripts/test-scripts.sh     - Test all scripts"
    
    wait_for_user
}

# Demo setup process
demo_setup() {
    print_step "Development Setup Process"
    
    print_info "First, make all scripts executable:"
    echo "  ./scripts/make-executable.sh"
    
    print_info "Then setup development environment:"
    echo "  ./scripts/setup-local.sh"
    echo ""
    echo "This will:"
    echo "  ✅ Check Node.js and Python prerequisites"
    echo "  ✅ Create Python virtual environment"
    echo "  ✅ Install backend dependencies"
    echo "  ✅ Install frontend dependencies"
    echo "  ✅ Setup database (SQLite for development)"
    echo "  ✅ Install Playwright browsers"
    echo "  ✅ Create environment files"
    
    wait_for_user
}

# Demo run process
demo_run() {
    print_step "Running Application Locally"
    
    print_info "Option 1: Full featured run script"
    echo "  ./scripts/run-local.sh"
    echo ""
    echo "Features:"
    echo "  ✅ Starts both backend and frontend"
    echo "  ✅ Port conflict detection"
    echo "  ✅ Service monitoring"
    echo "  ✅ Auto database migration"
    echo "  ✅ Redis service checking"
    echo "  ✅ Graceful shutdown with Ctrl+C"
    echo ""
    
    print_info "Option 2: Quick development start"
    echo "  ./scripts/dev.sh"
    echo ""
    echo "Features:"
    echo "  ✅ Fast startup"
    echo "  ✅ Simple mode switching"
    echo "  ✅ Minimal overhead"
    echo ""
    
    print_info "Available options:"
    echo "  ./scripts/run-local.sh --backend-only    # Only backend"
    echo "  ./scripts/run-local.sh --frontend-only   # Only frontend"
    echo "  ./scripts/run-local.sh --port 8080       # Custom port"
    echo "  ./scripts/dev.sh backend                 # Quick backend only"
    echo "  ./scripts/dev.sh frontend                # Quick frontend only"
    
    wait_for_user
}

# Demo build process
demo_build() {
    print_step "Building Application"
    
    print_info "Development build:"
    echo "  ./scripts/build-local.sh"
    echo ""
    echo "Features:"
    echo "  ✅ Builds React frontend"
    echo "  ✅ Creates backend executable"
    echo "  ✅ Optional Electron packaging"
    echo "  ✅ Build verification"
    echo ""
    
    print_info "Production build:"
    echo "  ./scripts/build-production.sh"
    echo ""
    echo "Features:"
    echo "  ✅ Optimized frontend build"
    echo "  ✅ Production backend build"
    echo "  ✅ Docker images creation"
    echo "  ✅ Distribution packages"
    echo "  ✅ Deployment documentation"
    echo ""
    
    print_info "Build options:"
    echo "  ./scripts/build-local.sh --clean --with-tests"
    echo "  ./scripts/build-production.sh --with-tests --no-docker"
    
    wait_for_user
}

# Demo deployment
demo_deploy() {
    print_step "Deployment Options"
    
    print_info "Local deployment:"
    echo "  ./scripts/deploy.sh local traditional"
    echo ""
    
    print_info "Remote deployment:"
    echo "  ./scripts/deploy.sh remote docker -h server.com -u root"
    echo ""
    
    print_info "Docker deployment:"
    echo "  ./scripts/deploy.sh docker compose"
    echo ""
    
    print_info "Kubernetes deployment:"
    echo "  ./scripts/deploy.sh k8s docker"
    echo ""
    
    print_info "Production server setup:"
    echo "  sudo ./scripts/setup-production.sh"
    echo ""
    echo "Features:"
    echo "  ✅ System dependencies installation"
    echo "  ✅ Database setup (PostgreSQL)"
    echo "  ✅ Redis configuration"
    echo "  ✅ Nginx setup"
    echo "  ✅ Systemd services"
    echo "  ✅ Security configuration"
    
    wait_for_user
}

# Demo management interface
demo_management() {
    print_step "Unified Management Interface"
    
    print_info "The manage.sh script provides a unified interface for all operations:"
    echo ""
    echo "Setup:"
    echo "  ./scripts/manage.sh setup dev     # Development setup"
    echo "  ./scripts/manage.sh setup prod    # Production setup"
    echo ""
    echo "Run:"
    echo "  ./scripts/manage.sh run           # Run both backend and frontend"
    echo "  ./scripts/manage.sh run backend   # Run only backend"
    echo "  ./scripts/manage.sh run frontend  # Run only frontend"
    echo ""
    echo "Build:"
    echo "  ./scripts/manage.sh build dev     # Development build"
    echo "  ./scripts/manage.sh build prod    # Production build"
    echo ""
    echo "Deploy:"
    echo "  ./scripts/manage.sh deploy local  # Local deployment"
    echo ""
    echo "Control:"
    echo "  ./scripts/manage.sh start dev     # Start development"
    echo "  ./scripts/manage.sh stop all      # Stop all services"
    echo "  ./scripts/manage.sh status        # Show status"
    echo "  ./scripts/manage.sh logs          # Show logs"
    echo ""
    echo "Testing:"
    echo "  ./scripts/manage.sh test all      # Run all tests"
    echo "  ./scripts/manage.sh clean         # Clean builds"
    
    wait_for_user
}

# Demo typical workflow
demo_workflow() {
    print_step "Typical Development Workflow"
    
    print_info "1. Initial Setup (one time):"
    echo "  ./scripts/make-executable.sh"
    echo "  ./scripts/setup-local.sh"
    echo ""
    
    print_info "2. Daily Development:"
    echo "  ./scripts/dev.sh                 # Quick start"
    echo "  # ... do development work ..."
    echo "  Ctrl+C                           # Stop when done"
    echo ""
    
    print_info "3. Testing Changes:"
    echo "  ./scripts/manage.sh test all     # Run tests"
    echo "  ./scripts/build-local.sh         # Test build"
    echo ""
    
    print_info "4. Production Deployment:"
    echo "  ./scripts/build-production.sh    # Create production build"
    echo "  ./scripts/deploy.sh local traditional  # Deploy locally"
    echo "  # or"
    echo "  ./scripts/deploy.sh remote docker -h server.com  # Deploy to server"
    echo ""
    
    print_info "5. Monitoring:"
    echo "  ./scripts/manage.sh status       # Check status"
    echo "  ./scripts/manage.sh logs         # View logs"
    
    wait_for_user
}

# Demo troubleshooting
demo_troubleshooting() {
    print_step "Troubleshooting & Tips"
    
    print_info "Common Issues & Solutions:"
    echo ""
    echo "❌ Permission denied:"
    echo "  ./scripts/make-executable.sh"
    echo ""
    echo "❌ Port already in use:"
    echo "  ./scripts/run-local.sh --port 8080"
    echo "  ./scripts/manage.sh stop all"
    echo ""
    echo "❌ Dependencies missing:"
    echo "  ./scripts/setup-local.sh"
    echo ""
    echo "❌ Build fails:"
    echo "  ./scripts/build-local.sh --clean"
    echo ""
    echo "❌ Services not starting:"
    echo "  ./scripts/manage.sh status"
    echo "  ./scripts/manage.sh logs"
    echo ""
    
    print_info "Testing Scripts:"
    echo "  ./scripts/test-scripts.sh        # Verify all scripts work"
    echo ""
    
    print_info "Getting Help:"
    echo "  ./scripts/manage.sh --help       # Show all options"
    echo "  ./scripts/run-local.sh --help    # Script-specific help"
    echo "  cat scripts/README.md            # Detailed documentation"
    
    wait_for_user
}

# Main demo function
main() {
    case ${1:-all} in
        "scripts")
            show_scripts
            ;;
        "setup")
            demo_setup
            ;;
        "run")
            demo_run
            ;;
        "build")
            demo_build
            ;;
        "deploy")
            demo_deploy
            ;;
        "manage")
            demo_management
            ;;
        "workflow")
            demo_workflow
            ;;
        "troubleshoot")
            demo_troubleshooting
            ;;
        "all"|"")
            show_scripts
            demo_setup
            demo_run
            demo_build
            demo_deploy
            demo_management
            demo_workflow
            demo_troubleshooting
            ;;
        "help")
            echo "Usage: $0 [SECTION] [MODE]"
            echo ""
            echo "SECTIONS:"
            echo "  scripts       - Show available scripts"
            echo "  setup         - Demo setup process"
            echo "  run           - Demo run process"
            echo "  build         - Demo build process"
            echo "  deploy        - Demo deployment"
            echo "  manage        - Demo management interface"
            echo "  workflow      - Demo typical workflow"
            echo "  troubleshoot  - Demo troubleshooting"
            echo "  all           - Show all sections (default)"
            echo ""
            echo "MODES:"
            echo "  interactive   - Wait for user input (default)"
            echo "  auto          - Auto-advance"
            echo ""
            echo "Examples:"
            echo "  $0                    # Full interactive demo"
            echo "  $0 workflow auto      # Quick workflow demo"
            echo "  $0 run                # Just run demo"
            exit 0
            ;;
        *)
            print_warning "Unknown section: $1"
            echo "Use '$0 help' for available options"
            exit 1
            ;;
    esac
    
    print_success "🎉 Demo completed!"
    echo ""
    print_info "Ready to start development:"
    echo "  ./scripts/setup-local.sh    # Setup (if not done)"
    echo "  ./scripts/dev.sh            # Quick start"
    echo "  ./scripts/manage.sh         # Full management"
    echo ""
    print_info "For detailed documentation: cat scripts/README.md"
}

# Run demo
main "$@"
