@echo off
REM Quick development script for Facebook Automation Desktop Application (Windows)
REM Simplified version of run-local.bat for daily development

setlocal enabledelayedexpansion

echo ⚡ Quick Dev Start - Facebook Automation Desktop

REM Get project directory
set PROJECT_DIR=%~dp0..

REM Function definitions
:print_info
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Cleanup function
:cleanup
call :print_info "Stopping development servers..."
taskkill /f /im python.exe 2>nul >nul
taskkill /f /im node.exe 2>nul >nul
taskkill /f /im electron.exe 2>nul >nul
call :print_success "Stopped"
goto :eof

REM Quick checks
:check_setup
if not exist "%PROJECT_DIR%\backend\venv" (
    call :print_error "Setup not complete. Run: scripts\setup-local.bat"
    pause
    exit /b 1
)

if not exist "%PROJECT_DIR%\frontend\node_modules" (
    call :print_error "Frontend deps missing. Run: cd frontend && npm install"
    pause
    exit /b 1
)
goto :eof

REM Parse arguments
:parse_args
set MODE=both

if "%~1"=="backend" set MODE=backend
if "%~1"=="be" set MODE=backend
if "%~1"=="frontend" set MODE=frontend
if "%~1"=="fe" set MODE=frontend
if "%~1"=="both" set MODE=both
if "%~1"=="all" set MODE=both
if "%~1"=="" set MODE=both
if "%~1"=="stop" (
    call :cleanup
    pause
    exit /b 0
)
if "%~1"=="help" (
    echo Usage: %0 [backend^|frontend^|both^|stop]
    echo   backend/be  - Start only backend
    echo   frontend/fe - Start only frontend
    echo   both/all    - Start both (default)
    echo   stop        - Stop all services
    pause
    exit /b 0
)

goto :eof

REM Start backend
:start_backend
call :print_info "🐍 Starting backend..."
cd /d "%PROJECT_DIR%\backend"

REM Quick Redis check
tasklist | find "redis-server.exe" >nul
if errorlevel 1 (
    call :print_warning "Redis not running (install if needed)"
)

REM Start backend
start "Backend Server" cmd /c "venv\Scripts\activate.bat && uvicorn main:app --reload --host 0.0.0.0 --port 8000"

REM Wait for startup
timeout /t 2 /nobreak >nul

REM Check if started
netstat -an | find ":8000" | find "LISTENING" >nul
if not errorlevel 1 (
    call :print_success "Backend running: http://localhost:8000"
    set BACKEND_STARTED=true
) else (
    call :print_error "Backend failed to start"
    pause
    exit /b 1
)

goto :eof

REM Start frontend
:start_frontend
call :print_info "⚛️  Starting frontend..."
cd /d "%PROJECT_DIR%\frontend"

REM Set API URL
set REACT_APP_API_URL=http://localhost:8000
set REACT_APP_WS_URL=ws://localhost:8000

REM Start frontend
start "Frontend Electron" cmd /c "npm run dev"

REM Wait for startup
timeout /t 3 /nobreak >nul

REM Check if started
tasklist | find "node.exe" >nul
if not errorlevel 1 (
    call :print_success "Frontend running (Electron app should open)"
    set FRONTEND_STARTED=true
) else (
    call :print_error "Frontend failed to start"
    pause
    exit /b 1
)

goto :eof

REM Main execution
:main
call :check_setup
call :parse_args %*

call :print_info "Starting in %MODE% mode..."

REM Start services based on mode
if "%MODE%"=="backend" (
    call :start_backend
) else if "%MODE%"=="frontend" (
    call :start_frontend
) else if "%MODE%"=="both" (
    call :start_backend
    call :start_frontend
)

REM Show status
echo.
call :print_success "🚀 Development environment ready!"
echo.
if "%MODE%"=="backend" (
    echo   📡 Backend API: http://localhost:8000
    echo   📚 API Docs: http://localhost:8000/docs
) else if "%MODE%"=="frontend" (
    echo   🖥️  Desktop App: Electron window
) else if "%MODE%"=="both" (
    echo   📡 Backend API: http://localhost:8000
    echo   📚 API Docs: http://localhost:8000/docs
    echo   🖥️  Desktop App: Electron window
)
echo.
call :print_info "To stop services, run: %0 stop"
echo.

REM Keep window open
pause
goto :eof

REM Run main
call :main %*
