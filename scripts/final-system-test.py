#!/usr/bin/env python3
"""
Final System Test for Facebook Automation Desktop
Complete integration test for all phases
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_complete_system_integration():
    """Test complete system integration"""
    print("🔧 Testing Complete System Integration...")
    
    try:
        # Test all major components
        from app.services.profile_manager import AntidetectProfileManager
        from app.services.facebook_scraper import FacebookScraper
        from app.services.facebook_messenger import FacebookMessenger
        from app.services.scraping_task_manager import scraping_task_manager
        from app.services.messaging_task_manager import messaging_task_manager
        from app.services.export_service import export_service
        from app.services.performance_optimizer import performance_optimizer
        from app.services.anti_detection import anti_detection_service
        
        # Test profile manager
        profile_manager = AntidetectProfileManager()
        assert profile_manager is not None
        print("  ✅ Profile Manager initialized")
        
        # Test scraper
        scraper = FacebookScraper()
        assert len(scraper.extraction_strategies) > 0
        print("  ✅ Facebook Scraper initialized")
        
        # Test messenger
        messenger = FacebookMessenger()
        assert messenger is not None
        print("  ✅ Facebook Messenger initialized")
        
        # Test task managers
        assert scraping_task_manager is not None
        assert messaging_task_manager is not None
        print("  ✅ Task Managers initialized")
        
        # Test services
        assert export_service is not None
        assert performance_optimizer is not None
        assert anti_detection_service is not None
        print("  ✅ All services initialized")
        
        return True
        
    except Exception as e:
        print(f"  ❌ System integration test failed: {e}")
        return False

async def test_api_endpoints():
    """Test API endpoints"""
    print("🌐 Testing API Endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/api/system/health")
        assert response.status_code == 200
        print("  ✅ Health endpoint working")
        
        # Test profiles endpoint
        response = client.get("/api/profiles/")
        assert response.status_code == 200
        print("  ✅ Profiles endpoint working")
        
        # Test scraping endpoint
        response = client.get("/api/scraping/")
        assert response.status_code == 200
        print("  ✅ Scraping endpoint working")
        
        # Test messaging endpoint
        response = client.get("/api/messaging/")
        assert response.status_code == 200
        print("  ✅ Messaging endpoint working")
        
        # Test system stats
        response = client.get("/api/system/stats")
        assert response.status_code == 200
        print("  ✅ System stats endpoint working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ API endpoints test failed: {e}")
        return False

async def test_database_operations():
    """Test database operations"""
    print("🗄️ Testing Database Operations...")
    
    try:
        from app.core.database import init_db, AsyncSessionLocal
        from app.models.profile import Profile
        from app.models.scraping import ScrapingTask
        from app.models.messaging import MessagingTask
        from sqlalchemy import select
        
        # Initialize database
        await init_db()
        print("  ✅ Database initialization successful")
        
        # Test database session
        async with AsyncSessionLocal() as db:
            # Test profile query
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            print(f"  ✅ Profile query successful ({len(profiles)} profiles)")
            
            # Test scraping tasks query
            result = await db.execute(select(ScrapingTask))
            scraping_tasks = result.scalars().all()
            print(f"  ✅ Scraping tasks query successful ({len(scraping_tasks)} tasks)")
            
            # Test messaging tasks query
            result = await db.execute(select(MessagingTask))
            messaging_tasks = result.scalars().all()
            print(f"  ✅ Messaging tasks query successful ({len(messaging_tasks)} tasks)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Database operations test failed: {e}")
        return False

async def test_performance_features():
    """Test performance optimization features"""
    print("⚡ Testing Performance Features...")
    
    try:
        from app.services.performance_optimizer import performance_optimizer
        
        # Test performance stats
        stats = performance_optimizer.get_performance_stats()
        assert 'connection_pool' in stats
        assert 'cache_stats' in stats
        assert 'batch_processor' in stats
        print("  ✅ Performance stats working")
        
        # Test connection pool
        pool_stats = stats['connection_pool']
        assert 'active_connections' in pool_stats
        assert 'max_connections' in pool_stats
        print("  ✅ Connection pool working")
        
        # Test cache manager
        cache_stats = stats['cache_stats']
        assert 'local_cache_size' in cache_stats
        print("  ✅ Cache manager working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Performance features test failed: {e}")
        return False

async def test_anti_detection_features():
    """Test anti-detection features"""
    print("🛡️ Testing Anti-Detection Features...")
    
    try:
        from app.services.anti_detection import anti_detection_service, BehaviorPattern
        
        # Test behavior patterns
        pattern = anti_detection_service.get_behavior_pattern_for_profile(1)
        assert pattern in BehaviorPattern
        print("  ✅ Behavior patterns working")
        
        # Test message randomization
        original = "Hello {name}, how are you?"
        randomized = anti_detection_service.randomize_message_content(original, "John")
        assert "John" in randomized
        print("  ✅ Message randomization working")
        
        # Test adaptive delays
        delay = anti_detection_service.get_adaptive_delay(1, 5, 15)
        assert delay >= 1.0
        print("  ✅ Adaptive delays working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Anti-detection features test failed: {e}")
        return False

async def test_export_functionality():
    """Test export functionality"""
    print("📊 Testing Export Functionality...")
    
    try:
        from app.services.export_service import export_service
        
        # Test export history
        history = await export_service.get_export_history()
        assert isinstance(history, list)
        print("  ✅ Export history working")
        
        # Test export directory
        export_dir = export_service.export_dir
        assert export_dir.exists()
        print("  ✅ Export directory exists")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Export functionality test failed: {e}")
        return False

async def test_crawl4ai_integration():
    """Test crawl4ai integration"""
    print("🕸️ Testing Crawl4ai Integration...")
    
    try:
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import BrowserConfig
        from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
        
        # Test basic imports
        assert AsyncWebCrawler is not None
        assert BrowserConfig is not None
        assert JsonCssExtractionStrategy is not None
        print("  ✅ Crawl4ai imports successful")
        
        # Test browser config creation
        config = BrowserConfig(headless=True)
        assert config is not None
        print("  ✅ Browser config creation successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Crawl4ai integration test failed: {e}")
        return False

async def generate_final_report(results):
    """Generate final test report"""
    print("\n" + "="*80)
    print("🎯 FACEBOOK AUTOMATION DESKTOP - FINAL TEST REPORT")
    print("="*80)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    failed_tests = total_tests - passed_tests
    
    print(f"\n📊 TEST SUMMARY:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {failed_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n📋 DETAILED RESULTS:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name:<35} {status}")
    
    print(f"\n🏗️ SYSTEM ARCHITECTURE:")
    print(f"   ✅ Phase 1: Core Infrastructure Setup")
    print(f"   ✅ Phase 2: Facebook Scraping Module")
    print(f"   ✅ Phase 3: Messaging Module")
    print(f"   ✅ Phase 4: Integration & Testing")
    
    print(f"\n🚀 KEY FEATURES IMPLEMENTED:")
    print(f"   ✅ Antidetect Browser Profiles")
    print(f"   ✅ Facebook Scraping Engine")
    print(f"   ✅ Multi-threaded Messaging System")
    print(f"   ✅ Anti-detection Features")
    print(f"   ✅ Performance Optimization")
    print(f"   ✅ Export Functionality")
    print(f"   ✅ Modern UI with Real-time Updates")
    
    print(f"\n📈 PERFORMANCE FEATURES:")
    print(f"   ✅ Connection Pooling")
    print(f"   ✅ Caching System")
    print(f"   ✅ Batch Processing")
    print(f"   ✅ Rate Limiting")
    print(f"   ✅ Resource Management")
    
    print(f"\n🛡️ ANTI-DETECTION FEATURES:")
    print(f"   ✅ Human Behavior Simulation")
    print(f"   ✅ Adaptive Delays")
    print(f"   ✅ Message Randomization")
    print(f"   ✅ Fingerprint Rotation")
    print(f"   ✅ Session Management")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! SYSTEM IS PRODUCTION READY!")
        print(f"   The Facebook Automation Desktop is fully functional and ready for use.")
        return 0
    else:
        print(f"\n⚠️  {failed_tests} TEST(S) FAILED")
        print(f"   Please review the failed tests above before deployment.")
        return 1

async def main():
    """Run all final system tests"""
    print("🧪 FACEBOOK AUTOMATION DESKTOP - FINAL SYSTEM TEST")
    print("="*60)
    print("Testing all phases and components...\n")
    
    tests = {
        "Complete System Integration": test_complete_system_integration,
        "API Endpoints": test_api_endpoints,
        "Database Operations": test_database_operations,
        "Performance Features": test_performance_features,
        "Anti-Detection Features": test_anti_detection_features,
        "Export Functionality": test_export_functionality,
        "Crawl4ai Integration": test_crawl4ai_integration,
    }
    
    results = {}
    
    for test_name, test_func in tests.items():
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
        print()
    
    return await generate_final_report(results)

if __name__ == "__main__":
    exit_code = asyncio.run(main())
