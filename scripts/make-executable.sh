#!/bin/bash

# Make all scripts executable
# Facebook Automation Desktop Application

echo "🔧 Making all scripts executable..."

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Make all .sh files executable
find "$SCRIPT_DIR" -name "*.sh" -type f -exec chmod +x {} \;

echo "✅ All scripts are now executable!"

# List executable scripts
echo ""
echo "📋 Available scripts:"
echo "  Setup scripts:"
echo "    - setup-local.sh          (Linux/macOS development setup)"
echo "    - setup-local.bat         (Windows development setup)"
echo "    - setup-production.sh     (Production server setup)"
echo ""
echo "  Build scripts:"
echo "    - build-local.sh          (Development build)"
echo "    - build-local.bat         (Windows development build)"
echo "    - build-production.sh     (Production build)"
echo ""
echo "  Deployment scripts:"
echo "    - deploy.sh               (Automated deployment)"
echo ""
echo "  Utility scripts:"
echo "    - make-executable.sh      (This script)"
echo ""
echo "📖 For detailed usage, see scripts/README.md"
