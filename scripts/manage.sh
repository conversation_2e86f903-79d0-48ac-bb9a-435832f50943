#!/bin/bash

# Management script for Facebook Automation Desktop Application
# Unified interface for all operations

set -e

echo "🎯 Facebook Automation Desktop Application Manager"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get directories
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# Show help
show_help() {
    cat << EOF
Usage: $0 COMMAND [OPTIONS]

Facebook Automation Desktop Application Manager

COMMANDS:
  setup           Setup development environment
  build           Build application
  deploy          Deploy application
  run             Run application locally
  start           Start development servers
  stop            Stop running services
  test            Run tests
  clean           Clean build artifacts
  status          Show application status
  logs            Show application logs

SETUP OPTIONS:
  setup dev       Setup development environment
  setup prod      Setup production environment

BUILD OPTIONS:
  build dev       Build for development
  build prod      Build for production
  build clean     Clean and build

DEPLOY OPTIONS:
  deploy local    Deploy locally
  deploy remote   Deploy to remote server
  deploy docker   Deploy with Docker

RUN OPTIONS:
  run             Run both backend and frontend
  run backend     Run only backend
  run frontend    Run only frontend

START/STOP OPTIONS:
  start dev       Start development servers
  start prod      Start production services
  stop all        Stop all services

TEST OPTIONS:
  test backend    Run backend tests
  test frontend   Run frontend tests
  test all        Run all tests

EXAMPLES:
  $0 setup dev                    # Setup development environment
  $0 run                          # Run application locally
  $0 build prod                   # Build for production
  $0 deploy local                 # Deploy locally
  $0 start dev                    # Start development servers
  $0 test all                     # Run all tests

EOF
}

# Setup commands
cmd_setup() {
    case ${1:-dev} in
        dev|development)
            print_status "Setting up development environment..."
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
                "$SCRIPT_DIR/setup-local.bat"
            else
                "$SCRIPT_DIR/setup-local.sh"
            fi
            ;;
        prod|production)
            print_status "Setting up production environment..."
            "$SCRIPT_DIR/setup-production.sh" "${@:2}"
            ;;
        *)
            print_error "Unknown setup target: $1"
            echo "Available: dev, prod"
            exit 1
            ;;
    esac
}

# Build commands
cmd_build() {
    case ${1:-dev} in
        dev|development)
            print_status "Building for development..."
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
                "$SCRIPT_DIR/build-local.bat" "${@:2}"
            else
                "$SCRIPT_DIR/build-local.sh" "${@:2}"
            fi
            ;;
        prod|production)
            print_status "Building for production..."
            "$SCRIPT_DIR/build-production.sh" "${@:2}"
            ;;
        clean)
            print_status "Cleaning and building..."
            cmd_clean
            cmd_build dev "${@:2}"
            ;;
        *)
            print_error "Unknown build target: $1"
            echo "Available: dev, prod, clean"
            exit 1
            ;;
    esac
}

# Run commands
cmd_run() {
    case ${1:-both} in
        backend|be)
            print_status "Running backend only..."
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
                "$SCRIPT_DIR/run-local.bat" --backend-only
            else
                "$SCRIPT_DIR/run-local.sh" --backend-only
            fi
            ;;
        frontend|fe)
            print_status "Running frontend only..."
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
                "$SCRIPT_DIR/run-local.bat" --frontend-only
            else
                "$SCRIPT_DIR/run-local.sh" --frontend-only
            fi
            ;;
        both|all|"")
            print_status "Running full application..."
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
                "$SCRIPT_DIR/run-local.bat" "${@:2}"
            else
                "$SCRIPT_DIR/run-local.sh" "${@:2}"
            fi
            ;;
        *)
            print_error "Unknown run target: $1"
            echo "Available: backend, frontend, both"
            exit 1
            ;;
    esac
}

# Deploy commands
cmd_deploy() {
    case ${1:-local} in
        local)
            print_status "Deploying locally..."
            "$SCRIPT_DIR/deploy.sh" local traditional "${@:2}"
            ;;
        remote)
            print_status "Deploying to remote server..."
            "$SCRIPT_DIR/deploy.sh" remote traditional "${@:2}"
            ;;
        docker)
            print_status "Deploying with Docker..."
            "$SCRIPT_DIR/deploy.sh" docker compose "${@:2}"
            ;;
        *)
            print_error "Unknown deploy target: $1"
            echo "Available: local, remote, docker"
            exit 1
            ;;
    esac
}

# Start commands
cmd_start() {
    case ${1:-dev} in
        dev|development)
            print_status "Starting development servers..."
            cd "$PROJECT_DIR/frontend"
            npm run dev &
            DEV_PID=$!
            
            print_status "Development servers started (PID: $DEV_PID)"
            print_status "Press Ctrl+C to stop"
            
            # Wait for interrupt
            trap "kill $DEV_PID 2>/dev/null; exit 0" INT
            wait $DEV_PID
            ;;
        prod|production)
            print_status "Starting production services..."
            sudo systemctl start facebook-automation-backend
            sudo systemctl start facebook-automation-worker
            sudo systemctl start nginx
            print_success "Production services started"
            ;;
        *)
            print_error "Unknown start target: $1"
            echo "Available: dev, prod"
            exit 1
            ;;
    esac
}

# Stop commands
cmd_stop() {
    case ${1:-all} in
        all)
            print_status "Stopping all services..."
            
            # Stop development servers
            pkill -f "npm run dev" 2>/dev/null || true
            pkill -f "uvicorn main:app" 2>/dev/null || true
            pkill -f "electron" 2>/dev/null || true
            
            # Stop production services
            sudo systemctl stop facebook-automation-backend 2>/dev/null || true
            sudo systemctl stop facebook-automation-worker 2>/dev/null || true
            
            print_success "All services stopped"
            ;;
        dev|development)
            print_status "Stopping development servers..."
            pkill -f "npm run dev" 2>/dev/null || true
            pkill -f "uvicorn main:app" 2>/dev/null || true
            pkill -f "electron" 2>/dev/null || true
            print_success "Development servers stopped"
            ;;
        prod|production)
            print_status "Stopping production services..."
            sudo systemctl stop facebook-automation-backend
            sudo systemctl stop facebook-automation-worker
            print_success "Production services stopped"
            ;;
        *)
            print_error "Unknown stop target: $1"
            echo "Available: all, dev, prod"
            exit 1
            ;;
    esac
}

# Test commands
cmd_test() {
    case ${1:-all} in
        backend)
            print_status "Running backend tests..."
            cd "$PROJECT_DIR/backend"
            source venv/bin/activate 2>/dev/null || true
            python -m pytest tests/ -v
            ;;
        frontend)
            print_status "Running frontend tests..."
            cd "$PROJECT_DIR/frontend"
            npm test
            ;;
        all)
            print_status "Running all tests..."
            cmd_test backend
            cmd_test frontend
            ;;
        *)
            print_error "Unknown test target: $1"
            echo "Available: backend, frontend, all"
            exit 1
            ;;
    esac
}

# Clean command
cmd_clean() {
    print_status "Cleaning build artifacts..."
    
    # Clean frontend
    rm -rf "$PROJECT_DIR/frontend/dist"
    rm -rf "$PROJECT_DIR/frontend/build"
    rm -rf "$PROJECT_DIR/frontend/node_modules/.cache"
    
    # Clean backend
    rm -rf "$PROJECT_DIR/backend/dist"
    rm -rf "$PROJECT_DIR/backend/build"
    rm -rf "$PROJECT_DIR/backend/__pycache__"
    find "$PROJECT_DIR/backend" -name "*.pyc" -delete 2>/dev/null || true
    
    # Clean project
    rm -rf "$PROJECT_DIR/build"
    rm -rf "$PROJECT_DIR/dist"
    
    print_success "Build artifacts cleaned"
}

# Status command
cmd_status() {
    print_status "Application Status:"
    echo ""
    
    # Check development processes
    if pgrep -f "npm run dev" > /dev/null; then
        print_success "Development frontend: Running"
    else
        print_warning "Development frontend: Not running"
    fi
    
    if pgrep -f "uvicorn main:app" > /dev/null; then
        print_success "Development backend: Running"
    else
        print_warning "Development backend: Not running"
    fi
    
    # Check production services
    if systemctl is-active --quiet facebook-automation-backend 2>/dev/null; then
        print_success "Production backend: Running"
    else
        print_warning "Production backend: Not running"
    fi
    
    if systemctl is-active --quiet facebook-automation-worker 2>/dev/null; then
        print_success "Production worker: Running"
    else
        print_warning "Production worker: Not running"
    fi
    
    if systemctl is-active --quiet nginx 2>/dev/null; then
        print_success "Nginx: Running"
    else
        print_warning "Nginx: Not running"
    fi
    
    echo ""
}

# Logs command
cmd_logs() {
    case ${1:-all} in
        backend)
            if [ -f "/var/log/facebook-automation/backend.log" ]; then
                tail -f /var/log/facebook-automation/backend.log
            else
                journalctl -u facebook-automation-backend -f
            fi
            ;;
        worker)
            journalctl -u facebook-automation-worker -f
            ;;
        nginx)
            tail -f /var/log/nginx/access.log /var/log/nginx/error.log
            ;;
        all)
            print_status "Showing all logs (Ctrl+C to stop)..."
            journalctl -u facebook-automation-backend -u facebook-automation-worker -f
            ;;
        *)
            print_error "Unknown log target: $1"
            echo "Available: backend, worker, nginx, all"
            exit 1
            ;;
    esac
}

# Main function
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    case $1 in
        setup)
            cmd_setup "${@:2}"
            ;;
        build)
            cmd_build "${@:2}"
            ;;
        deploy)
            cmd_deploy "${@:2}"
            ;;
        run)
            cmd_run "${@:2}"
            ;;
        start)
            cmd_start "${@:2}"
            ;;
        stop)
            cmd_stop "${@:2}"
            ;;
        test)
            cmd_test "${@:2}"
            ;;
        clean)
            cmd_clean
            ;;
        status)
            cmd_status
            ;;
        logs)
            cmd_logs "${@:2}"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
