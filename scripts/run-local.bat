@echo off
REM Run Facebook Automation Desktop Application locally (Windows)
REM Starts both backend and frontend development servers

setlocal enabledelayedexpansion

echo 🚀 Starting Facebook Automation Desktop Application locally...

REM Configuration
set BACKEND_PORT=8000
set FRONTEND_PORT=3000
set PROJECT_DIR=%~dp0..

REM PIDs for cleanup (Windows doesn't have easy PID management like Unix)
set BACKEND_STARTED=false
set FRONTEND_STARTED=false

REM Function to print colored output
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Cleanup function
:cleanup
call :print_status "Shutting down services..."

REM Kill processes by name
taskkill /f /im python.exe 2>nul >nul
taskkill /f /im node.exe 2>nul >nul
taskkill /f /im electron.exe 2>nul >nul

call :print_success "All services stopped"
goto :eof

REM Check if ports are available
:check_ports
call :print_status "Checking port availability..."

netstat -an | find ":%BACKEND_PORT%" | find "LISTENING" >nul
if not errorlevel 1 (
    call :print_error "Port %BACKEND_PORT% is already in use"
    call :print_status "Please stop the service using port %BACKEND_PORT% or use a different port"
    exit /b 1
)

netstat -an | find ":%FRONTEND_PORT%" | find "LISTENING" >nul
if not errorlevel 1 (
    call :print_warning "Port %FRONTEND_PORT% is already in use"
    call :print_status "Frontend will try to use next available port"
)

call :print_success "Ports are available"
goto :eof

REM Check prerequisites
:check_prerequisites
call :print_status "Checking prerequisites..."

if not exist "%PROJECT_DIR%\backend\venv" (
    call :print_error "Backend virtual environment not found"
    call :print_status "Please run setup first: scripts\setup-local.bat"
    exit /b 1
)

if not exist "%PROJECT_DIR%\frontend\node_modules" (
    call :print_error "Frontend dependencies not found"
    call :print_status "Please run setup first: scripts\setup-local.bat"
    exit /b 1
)

call :print_success "Prerequisites check passed"
goto :eof

REM Start backend server
:start_backend
call :print_status "Starting backend server..."

cd /d "%PROJECT_DIR%\backend"

if not exist "main.py" (
    call :print_error "Backend main.py not found"
    exit /b 1
)

call :print_status "Starting FastAPI server on port %BACKEND_PORT%..."

REM Activate virtual environment and start server
start "Backend Server" cmd /c "venv\Scripts\activate.bat && uvicorn main:app --reload --host 0.0.0.0 --port %BACKEND_PORT%"

REM Wait for server to start
timeout /t 3 /nobreak >nul

REM Check if backend started (simple check)
netstat -an | find ":%BACKEND_PORT%" | find "LISTENING" >nul
if not errorlevel 1 (
    call :print_success "Backend server started"
    call :print_status "Backend API: http://localhost:%BACKEND_PORT%"
    call :print_status "API Docs: http://localhost:%BACKEND_PORT%/docs"
    set BACKEND_STARTED=true
) else (
    call :print_error "Failed to start backend server"
    exit /b 1
)

goto :eof

REM Start frontend server
:start_frontend
call :print_status "Starting frontend server..."

cd /d "%PROJECT_DIR%\frontend"

if not exist "package.json" (
    call :print_error "Frontend package.json not found"
    exit /b 1
)

REM Set environment variables
set REACT_APP_API_URL=http://localhost:%BACKEND_PORT%
set REACT_APP_WS_URL=ws://localhost:%BACKEND_PORT%

call :print_status "Starting React development server..."

REM Check if this is an Electron app
findstr /c:"electron" package.json >nul
if not errorlevel 1 (
    call :print_status "Detected Electron app, starting with Electron..."
    start "Frontend Electron" cmd /c "npm run dev"
) else (
    REM Regular React app
    start "Frontend React" cmd /c "npm start"
)

REM Wait for server to start
timeout /t 5 /nobreak >nul

REM Check if frontend started (simple check for node process)
tasklist | find "node.exe" >nul
if not errorlevel 1 (
    call :print_success "Frontend server started"
    findstr /c:"electron" package.json >nul
    if not errorlevel 1 (
        call :print_status "Electron app should open automatically"
    ) else (
        call :print_status "Frontend: http://localhost:%FRONTEND_PORT%"
    )
    set FRONTEND_STARTED=true
) else (
    call :print_error "Failed to start frontend server"
    exit /b 1
)

goto :eof

REM Start database services
:start_database
call :print_status "Checking database services..."

REM Check if Redis is needed
findstr /c:"redis" "%PROJECT_DIR%\backend\requirements.txt" >nul 2>&1
if not errorlevel 1 (
    tasklist | find "redis-server.exe" >nul
    if errorlevel 1 (
        call :print_warning "Redis is not running"
        call :print_status "Please start Redis manually if needed"
        call :print_status "Download Redis for Windows from: https://github.com/microsoftarchive/redis/releases"
    ) else (
        call :print_success "Redis is running"
    )
)

REM Run database migrations if needed
if exist "%PROJECT_DIR%\backend\alembic.ini" (
    call :print_status "Running database migrations..."
    cd /d "%PROJECT_DIR%\backend"
    call venv\Scripts\activate.bat
    alembic upgrade head 2>nul || call :print_warning "Migration failed or not needed"
)

goto :eof

REM Show running services status
:show_status
call :print_status "Service Status:"
echo.

if "%BACKEND_STARTED%"=="true" (
    netstat -an | find ":%BACKEND_PORT%" | find "LISTENING" >nul
    if not errorlevel 1 (
        call :print_success "✅ Backend: Running - http://localhost:%BACKEND_PORT%"
    ) else (
        call :print_error "❌ Backend: Not running"
    )
) else (
    call :print_error "❌ Backend: Not started"
)

if "%FRONTEND_STARTED%"=="true" (
    tasklist | find "node.exe" >nul
    if not errorlevel 1 (
        findstr /c:"electron" "%PROJECT_DIR%\frontend\package.json" >nul 2>&1
        if not errorlevel 1 (
            call :print_success "✅ Frontend: Running - Electron App"
        ) else (
            call :print_success "✅ Frontend: Running - http://localhost:%FRONTEND_PORT%"
        )
    ) else (
        call :print_error "❌ Frontend: Not running"
    )
) else (
    call :print_error "❌ Frontend: Not started"
)

tasklist | find "redis-server.exe" >nul
if not errorlevel 1 (
    call :print_success "✅ Redis: Running"
) else (
    call :print_warning "⚠️  Redis: Not running"
)

echo.
goto :eof

REM Show help
:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Start Facebook Automation Desktop Application locally
echo.
echo OPTIONS:
echo   --backend-only      Start only backend server
echo   --frontend-only     Start only frontend server
echo   --port PORT         Backend port (default: %BACKEND_PORT%)
echo   --no-db             Skip database setup
echo   --status            Show current service status
echo   --stop              Stop all running services
echo   --help              Show this help message
echo.
echo EXAMPLES:
echo   %0                          # Start both backend and frontend
echo   %0 --backend-only           # Start only backend
echo   %0 --port 8080              # Use custom backend port
echo   %0 --status                 # Show service status
echo   %0 --stop                   # Stop all services
echo.
goto :eof

REM Stop all services
:stop_services
call :print_status "Stopping all local services..."

REM Kill by process name
taskkill /f /im python.exe 2>nul >nul
taskkill /f /im node.exe 2>nul >nul
taskkill /f /im electron.exe 2>nul >nul

call :print_success "All services stopped"
goto :eof

REM Monitor services (simplified for Windows)
:monitor_services
call :print_status "Services started. Press Ctrl+C to stop all services."
echo.
call :print_status "To stop services manually, run: %0 --stop"
echo.

REM Show final status
call :show_status

call :print_success "🎉 Application started successfully!"
echo.
call :print_status "Available endpoints:"
if not "%1"=="--frontend-only" (
    echo   📡 Backend API: http://localhost:%BACKEND_PORT%
    echo   📚 API Documentation: http://localhost:%BACKEND_PORT%/docs
)
if not "%1"=="--backend-only" (
    findstr /c:"electron" "%PROJECT_DIR%\frontend\package.json" >nul 2>&1
    if not errorlevel 1 (
        echo   🖥️  Desktop App: Electron window should open
    ) else (
        echo   🌐 Frontend: http://localhost:%FRONTEND_PORT%
    )
)
echo.

REM Keep window open
pause
goto :eof

REM Main function
:main
REM Parse arguments
set BACKEND_ONLY=false
set FRONTEND_ONLY=false
set SKIP_DB=false

:parse_args
if "%~1"=="" goto :start_services
if "%~1"=="--backend-only" (
    set BACKEND_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--frontend-only" (
    set FRONTEND_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set BACKEND_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--no-db" (
    set SKIP_DB=true
    shift
    goto :parse_args
)
if "%~1"=="--status" (
    call :show_status
    pause
    exit /b 0
)
if "%~1"=="--stop" (
    call :stop_services
    pause
    exit /b 0
)
if "%~1"=="--help" (
    call :show_help
    pause
    exit /b 0
)
call :print_warning "Unknown option: %~1"
shift
goto :parse_args

:start_services
REM Validate conflicting options
if "%BACKEND_ONLY%"=="true" if "%FRONTEND_ONLY%"=="true" (
    call :print_error "Cannot use --backend-only and --frontend-only together"
    pause
    exit /b 1
)

call :print_status "Starting Facebook Automation Desktop Application..."
call :print_status "Project directory: %PROJECT_DIR%"

REM Run checks
call :check_prerequisites
if errorlevel 1 (
    pause
    exit /b 1
)

call :check_ports
if errorlevel 1 (
    pause
    exit /b 1
)

REM Start database services
if "%SKIP_DB%"=="false" (
    call :start_database
)

REM Start services based on options
if "%FRONTEND_ONLY%"=="false" (
    call :start_backend
    if errorlevel 1 (
        pause
        exit /b 1
    )
)

if "%BACKEND_ONLY%"=="false" (
    call :start_frontend
    if errorlevel 1 (
        pause
        exit /b 1
    )
)

REM Wait a moment then show status
timeout /t 2 /nobreak >nul

REM Monitor services
call :monitor_services

goto :eof

REM Set up Ctrl+C handler
if "%~1"=="--cleanup" (
    call :cleanup
    exit /b 0
)

REM Run main function
call :main %*
