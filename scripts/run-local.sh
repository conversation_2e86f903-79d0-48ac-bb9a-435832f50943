#!/bin/bash

# Run Facebook Automation Desktop Application locally
# Starts both backend and frontend development servers

set -e  # Exit on any error

echo "🚀 Starting Facebook Automation Desktop Application locally..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BACKEND_PORT=8000
FRONTEND_PORT=3000
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# PIDs for cleanup
BACKEND_PID=""
FRONTEND_PID=""

# Cleanup function
cleanup() {
    print_status "Shutting down services..."
    
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend stopped"
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend stopped"
    fi
    
    # Kill any remaining processes
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "electron" 2>/dev/null || true
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if ports are available
check_ports() {
    print_status "Checking port availability..."
    
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "Port $BACKEND_PORT is already in use"
        print_status "Please stop the service using port $BACKEND_PORT or use a different port"
        exit 1
    fi
    
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Port $FRONTEND_PORT is already in use"
        print_status "Frontend will try to use next available port"
    fi
    
    print_success "Ports are available"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if setup has been run
    if [ ! -d "$PROJECT_DIR/backend/venv" ]; then
        print_error "Backend virtual environment not found"
        print_status "Please run setup first: ./scripts/setup-local.sh"
        exit 1
    fi
    
    if [ ! -d "$PROJECT_DIR/frontend/node_modules" ]; then
        print_error "Frontend dependencies not found"
        print_status "Please run setup first: ./scripts/setup-local.sh"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Start backend server
start_backend() {
    print_status "Starting backend server..."
    
    cd "$PROJECT_DIR/backend"
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Check if main.py exists
    if [ ! -f "main.py" ]; then
        print_error "Backend main.py not found"
        exit 1
    fi
    
    # Start backend with uvicorn
    print_status "Starting FastAPI server on port $BACKEND_PORT..."
    uvicorn main:app --reload --host 0.0.0.0 --port $BACKEND_PORT &
    BACKEND_PID=$!
    
    # Wait a moment for server to start
    sleep 3
    
    # Check if backend started successfully
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "Backend server started (PID: $BACKEND_PID)"
        print_status "Backend API: http://localhost:$BACKEND_PORT"
        print_status "API Docs: http://localhost:$BACKEND_PORT/docs"
    else
        print_error "Failed to start backend server"
        exit 1
    fi
}

# Start frontend server
start_frontend() {
    print_status "Starting frontend server..."
    
    cd "$PROJECT_DIR/frontend"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "Frontend package.json not found"
        exit 1
    fi
    
    # Set environment variables
    export REACT_APP_API_URL="http://localhost:$BACKEND_PORT"
    export REACT_APP_WS_URL="ws://localhost:$BACKEND_PORT"
    
    # Start frontend development server
    print_status "Starting React development server..."
    
    # Check if this is an Electron app
    if grep -q "electron" package.json; then
        print_status "Detected Electron app, starting with Electron..."
        npm run dev &
        FRONTEND_PID=$!
    else
        # Regular React app
        npm start &
        FRONTEND_PID=$!
    fi
    
    # Wait a moment for server to start
    sleep 5
    
    # Check if frontend started successfully
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "Frontend server started (PID: $FRONTEND_PID)"
        if grep -q "electron" package.json; then
            print_status "Electron app should open automatically"
        else
            print_status "Frontend: http://localhost:$FRONTEND_PORT"
        fi
    else
        print_error "Failed to start frontend server"
        exit 1
    fi
}

# Start database services
start_database() {
    print_status "Checking database services..."
    
    # Check if Redis is needed and running
    if grep -q "redis" "$PROJECT_DIR/backend/requirements.txt" 2>/dev/null; then
        if ! pgrep redis-server > /dev/null; then
            print_warning "Redis is not running"
            print_status "Attempting to start Redis..."
            
            # Try to start Redis
            if command -v redis-server &> /dev/null; then
                redis-server --daemonize yes --port 6379 2>/dev/null || true
                sleep 2
                if pgrep redis-server > /dev/null; then
                    print_success "Redis started"
                else
                    print_warning "Could not start Redis automatically"
                    print_status "Please start Redis manually: redis-server"
                fi
            else
                print_warning "Redis not installed"
                print_status "Install Redis: sudo apt-get install redis-server (Ubuntu) or brew install redis (macOS)"
            fi
        else
            print_success "Redis is running"
        fi
    fi
    
    # Run database migrations if needed
    if [ -f "$PROJECT_DIR/backend/alembic.ini" ]; then
        print_status "Running database migrations..."
        cd "$PROJECT_DIR/backend"
        source venv/bin/activate
        alembic upgrade head 2>/dev/null || print_warning "Migration failed or not needed"
    fi
}

# Show running services status
show_status() {
    print_status "Service Status:"
    echo ""
    
    if [ -n "$BACKEND_PID" ] && kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "✅ Backend: Running (PID: $BACKEND_PID) - http://localhost:$BACKEND_PORT"
    else
        print_error "❌ Backend: Not running"
    fi
    
    if [ -n "$FRONTEND_PID" ] && kill -0 $FRONTEND_PID 2>/dev/null; then
        if grep -q "electron" "$PROJECT_DIR/frontend/package.json" 2>/dev/null; then
            print_success "✅ Frontend: Running (PID: $FRONTEND_PID) - Electron App"
        else
            print_success "✅ Frontend: Running (PID: $FRONTEND_PID) - http://localhost:$FRONTEND_PORT"
        fi
    else
        print_error "❌ Frontend: Not running"
    fi
    
    if pgrep redis-server > /dev/null; then
        print_success "✅ Redis: Running"
    else
        print_warning "⚠️  Redis: Not running"
    fi
    
    echo ""
}

# Monitor services
monitor_services() {
    print_status "Monitoring services... (Press Ctrl+C to stop)"
    echo ""
    
    while true; do
        # Check if backend is still running
        if [ -n "$BACKEND_PID" ] && ! kill -0 $BACKEND_PID 2>/dev/null; then
            print_error "Backend process died unexpectedly"
            cleanup
        fi
        
        # Check if frontend is still running
        if [ -n "$FRONTEND_PID" ] && ! kill -0 $FRONTEND_PID 2>/dev/null; then
            print_error "Frontend process died unexpectedly"
            cleanup
        fi
        
        sleep 5
    done
}

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Start Facebook Automation Desktop Application locally

OPTIONS:
  --backend-only      Start only backend server
  --frontend-only     Start only frontend server
  --port PORT         Backend port (default: $BACKEND_PORT)
  --no-db             Skip database setup
  --status            Show current service status
  --stop              Stop all running services
  --help              Show this help message

EXAMPLES:
  $0                          # Start both backend and frontend
  $0 --backend-only           # Start only backend
  $0 --port 8080              # Use custom backend port
  $0 --status                 # Show service status
  $0 --stop                   # Stop all services

EOF
}

# Stop all services
stop_services() {
    print_status "Stopping all local services..."
    
    # Kill by process name
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "electron" 2>/dev/null || true
    
    print_success "All services stopped"
}

# Main function
main() {
    # Parse arguments
    BACKEND_ONLY=false
    FRONTEND_ONLY=false
    SKIP_DB=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            --frontend-only)
                FRONTEND_ONLY=true
                shift
                ;;
            --port)
                BACKEND_PORT="$2"
                shift 2
                ;;
            --no-db)
                SKIP_DB=true
                shift
                ;;
            --status)
                show_status
                exit 0
                ;;
            --stop)
                stop_services
                exit 0
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done
    
    # Validate conflicting options
    if [ "$BACKEND_ONLY" = true ] && [ "$FRONTEND_ONLY" = true ]; then
        print_error "Cannot use --backend-only and --frontend-only together"
        exit 1
    fi
    
    print_status "Starting Facebook Automation Desktop Application..."
    print_status "Project directory: $PROJECT_DIR"
    
    # Run checks
    check_prerequisites
    check_ports
    
    # Start database services
    if [ "$SKIP_DB" = false ]; then
        start_database
    fi
    
    # Start services based on options
    if [ "$FRONTEND_ONLY" = false ]; then
        start_backend
    fi
    
    if [ "$BACKEND_ONLY" = false ]; then
        start_frontend
    fi
    
    # Show status
    sleep 2
    show_status
    
    print_success "🎉 Application started successfully!"
    echo ""
    print_status "Available endpoints:"
    if [ "$FRONTEND_ONLY" = false ]; then
        echo "  📡 Backend API: http://localhost:$BACKEND_PORT"
        echo "  📚 API Documentation: http://localhost:$BACKEND_PORT/docs"
    fi
    if [ "$BACKEND_ONLY" = false ]; then
        if grep -q "electron" "$PROJECT_DIR/frontend/package.json" 2>/dev/null; then
            echo "  🖥️  Desktop App: Electron window should open"
        else
            echo "  🌐 Frontend: http://localhost:$FRONTEND_PORT"
        fi
    fi
    echo ""
    
    # Monitor services
    monitor_services
}

# Run main function
main "$@"
