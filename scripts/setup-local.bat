@echo off
REM Setup script for local development environment (Windows)
REM Facebook Automation Desktop Application

setlocal enabledelayedexpansion

echo 🚀 Setting up Facebook Automation Desktop Application for local development...

REM Function to print colored output (Windows doesn't support colors easily, so we'll use simple text)
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Check if Node.js is installed
:check_nodejs
call :print_status "Checking Node.js installation..."
node --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do (
    set NODE_MAJOR=%%a
    set NODE_MAJOR=!NODE_MAJOR:v=!
)

if !NODE_MAJOR! LSS 16 (
    call :print_error "Node.js version 16+ is required. Current version: %NODE_VERSION%"
    exit /b 1
)

call :print_success "Node.js is installed"
goto :eof

REM Check if Python is installed
:check_python
call :print_status "Checking Python installation..."
python --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Python 3 is not installed. Please install Python 3.8+ from https://python.org/"
    exit /b 1
)

python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if errorlevel 1 (
    call :print_error "Python 3.8+ is required."
    exit /b 1
)

call :print_success "Python is installed"
goto :eof

REM Setup Python virtual environment
:setup_python_env
call :print_status "Setting up Python virtual environment..."

cd backend

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    python -m venv venv
    call :print_success "Created Python virtual environment"
) else (
    call :print_warning "Virtual environment already exists"
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Upgrade pip
python -m pip install --upgrade pip

REM Install dependencies
call :print_status "Installing Python dependencies..."
pip install -r requirements.txt

call :print_success "Python dependencies installed"

cd ..
goto :eof

REM Setup Node.js dependencies
:setup_nodejs_env
call :print_status "Setting up Node.js dependencies..."

cd frontend

REM Install frontend dependencies
call :print_status "Installing frontend dependencies..."
npm install

call :print_success "Frontend dependencies installed"

cd ..
goto :eof

REM Setup database
:setup_database
call :print_status "Setting up database..."

cd backend
call venv\Scripts\activate.bat

REM Run database migrations
call :print_status "Running database migrations..."
alembic upgrade head

call :print_success "Database setup completed"

cd ..
goto :eof

REM Install Playwright browsers
:setup_playwright
call :print_status "Installing Playwright browsers..."

cd backend
call venv\Scripts\activate.bat

playwright install

call :print_success "Playwright browsers installed"

cd ..
goto :eof

REM Create environment files
:create_env_files
call :print_status "Creating environment files..."

REM Backend .env file
if not exist "backend\.env" (
    (
        echo # Database
        echo DATABASE_URL=sqlite:///./app.db
        echo.
        echo # API Settings
        echo API_HOST=0.0.0.0
        echo API_PORT=8000
        echo DEBUG=true
        echo.
        echo # Security
        echo SECRET_KEY=your-secret-key-change-in-production
        echo ACCESS_TOKEN_EXPIRE_MINUTES=30
        echo.
        echo # Redis (for Celery^)
        echo REDIS_URL=redis://localhost:6379
        echo.
        echo # Logging
        echo LOG_LEVEL=INFO
    ) > backend\.env
    call :print_success "Created backend/.env file"
) else (
    call :print_warning "Backend .env file already exists"
)

REM Frontend .env file
if not exist "frontend\.env" (
    (
        echo # API Configuration
        echo REACT_APP_API_URL=http://localhost:8000
        echo REACT_APP_WS_URL=ws://localhost:8000
        echo.
        echo # Environment
        echo NODE_ENV=development
    ) > frontend\.env
    call :print_success "Created frontend/.env file"
) else (
    call :print_warning "Frontend .env file already exists"
)
goto :eof

REM Main setup function
:main
call :print_status "Starting local development setup..."

REM Check prerequisites
call :check_nodejs
if errorlevel 1 exit /b 1

call :check_python
if errorlevel 1 exit /b 1

REM Setup environments
call :setup_python_env
if errorlevel 1 exit /b 1

call :setup_nodejs_env
if errorlevel 1 exit /b 1

REM Setup database
call :setup_database
if errorlevel 1 exit /b 1

REM Setup Playwright
call :setup_playwright
if errorlevel 1 exit /b 1

REM Create environment files
call :create_env_files

call :print_success "🎉 Local development environment setup completed!"
echo.
call :print_status "To start development:"
echo   1. Start backend: cd backend ^&^& venv\Scripts\activate.bat ^&^& python main.py
echo   2. Start frontend: cd frontend ^&^& npm run dev
echo   3. Or start both: cd frontend ^&^& npm run dev
echo.
call :print_status "Backend will be available at: http://localhost:8000"
call :print_status "Frontend will be available as Electron app"

goto :eof

REM Run main function
call :main
