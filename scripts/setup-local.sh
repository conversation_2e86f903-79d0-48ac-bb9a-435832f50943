#!/bin/bash

# Setup script for local development environment
# Facebook Automation Desktop Application

set -e  # Exit on any error

echo "🚀 Setting up Facebook Automation Desktop Application for local development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js version 16+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Check if Python is installed
check_python() {
    print_status "Checking Python installation..."
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.8+ from https://python.org/"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_error "Python 3.8+ is required. Current version: $(python3 --version)"
        exit 1
    fi
    
    print_success "Python $(python3 --version) is installed"
}

# Get project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Setup Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."

    cd "$PROJECT_DIR/backend"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Created Python virtual environment"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    if [ -f "requirements-minimal.txt" ]; then
        print_status "Using minimal requirements for faster setup..."
        pip install -r requirements-minimal.txt
    else
        pip install -r requirements.txt
    fi
    
    print_success "Python dependencies installed"

    cd "$PROJECT_DIR"
}

# Setup Node.js dependencies
setup_nodejs_env() {
    print_status "Setting up Node.js dependencies..."

    cd "$PROJECT_DIR/frontend"
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    npm install
    
    print_success "Frontend dependencies installed"

    cd "$PROJECT_DIR"
}

# Setup database
setup_database() {
    print_status "Setting up database..."

    cd "$PROJECT_DIR/backend"
    source venv/bin/activate
    
    # Run database migrations
    print_status "Running database migrations..."
    alembic upgrade head
    
    print_success "Database setup completed"

    cd "$PROJECT_DIR"
}

# Install Playwright browsers
setup_playwright() {
    print_status "Installing Playwright browsers..."

    cd "$PROJECT_DIR/backend"
    source venv/bin/activate

    # Check if playwright is available
    if command -v playwright &> /dev/null; then
        playwright install
        print_success "Playwright browsers installed"
    else
        print_warning "Playwright not found in requirements, skipping browser installation"
        print_status "To install Playwright later: pip install playwright && playwright install"
    fi

    cd "$PROJECT_DIR"
}

# Create environment files
create_env_files() {
    print_status "Creating environment files..."
    
    # Backend .env file
    if [ ! -f "$PROJECT_DIR/backend/.env" ]; then
        cat > "$PROJECT_DIR/backend/.env" << EOF
# Database
DATABASE_URL=sqlite:///./app.db

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# Security
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis (for Celery)
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
EOF
        print_success "Created backend/.env file"
    else
        print_warning "Backend .env file already exists"
    fi
    
    # Frontend .env file
    if [ ! -f "$PROJECT_DIR/frontend/.env" ]; then
        cat > "$PROJECT_DIR/frontend/.env" << EOF
# API Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000

# Environment
NODE_ENV=development
EOF
        print_success "Created frontend/.env file"
    else
        print_warning "Frontend .env file already exists"
    fi
}

# Main setup function
main() {
    print_status "Starting local development setup..."
    
    # Check prerequisites
    check_nodejs
    check_python
    
    # Setup environments
    setup_python_env
    setup_nodejs_env
    
    # Setup database
    setup_database
    
    # Setup Playwright
    setup_playwright
    
    # Create environment files
    create_env_files
    
    print_success "🎉 Local development environment setup completed!"
    echo ""
    print_status "To start development:"
    echo "  1. Quick start: ./scripts/dev.sh"
    echo "  2. Full featured: ./scripts/run-local.sh"
    echo "  3. Manual backend: cd backend && source venv/bin/activate && python main.py"
    echo "  4. Manual frontend: cd frontend && npm run dev"
    echo ""
    print_status "Backend will be available at: http://localhost:8000"
    print_status "Frontend will be available as Electron app"
}

# Run main function
main "$@"
