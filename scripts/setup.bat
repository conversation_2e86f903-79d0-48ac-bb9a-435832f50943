@echo off
REM Facebook Automation Desktop - Setup Script for Windows

echo 🚀 Setting up Facebook Automation Desktop...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 16+ first.
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.8+ first.
    pause
    exit /b 1
)

echo ✅ Node.js version:
node --version
echo ✅ Python version:
python --version

REM Install Node.js dependencies
echo 📦 Installing Node.js dependencies...
npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Setup Python virtual environment
echo 🐍 Setting up Python virtual environment...
cd backend

if not exist "venv" (
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install Python dependencies
echo 📦 Installing Python dependencies...
python -m pip install --upgrade pip
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)

REM Install crawl4ai
echo 🕷️ Installing crawl4ai...
cd ..\crawl4ai
pip install -e .

if %errorlevel% neq 0 (
    echo ❌ Failed to install crawl4ai
    pause
    exit /b 1
)

cd ..

REM Install Playwright browsers
echo 🎭 Installing Playwright browsers...
python -m playwright install chromium

if %errorlevel% neq 0 (
    echo ❌ Failed to install Playwright browsers
    pause
    exit /b 1
)

REM Create data directories
echo 📁 Creating data directories...
if not exist "backend\data" mkdir backend\data
if not exist "backend\data\profiles" mkdir backend\data\profiles
if not exist "backend\data\exports" mkdir backend\data\exports
if not exist "backend\data\logs" mkdir backend\data\logs

REM Initialize database
echo 🗄️ Initializing database...
cd backend
python -c "import asyncio; from app.core.database import init_db; asyncio.run(init_db()); print('Database initialized successfully')"

if %errorlevel% neq 0 (
    echo ❌ Failed to initialize database
    pause
    exit /b 1
)

cd ..

echo ✅ Setup completed successfully!
echo.
echo 🎯 Next steps:
echo 1. Start Redis server (if using caching): redis-server
echo 2. Run the application: npm run dev
echo.
echo 📚 For more information, see README.md
pause
