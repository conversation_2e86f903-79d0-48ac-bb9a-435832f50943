#!/usr/bin/env python3
"""
Test script for Facebook Automation Backend
"""

import asyncio
import sys
import os
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_database():
    """Test database initialization"""
    print("🗄️ Testing database...")
    try:
        from app.core.database import init_db, get_db
        await init_db()
        print("✅ Database initialization successful")
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

async def test_profile_manager():
    """Test profile manager"""
    print("👤 Testing profile manager...")
    try:
        from app.services.profile_manager import AntidetectProfileManager
        
        manager = AntidetectProfileManager()
        
        # Test fingerprint generation
        from app.services.fingerprint_generator import FingerprintGenerator
        fingerprint_gen = FingerprintGenerator()
        fingerprint = await fingerprint_gen.generate_fingerprint()
        
        if fingerprint and 'user_agent' in fingerprint:
            print("✅ Profile manager test successful")
            return True
        else:
            print("❌ Profile manager test failed: Invalid fingerprint")
            return False
            
    except Exception as e:
        print(f"❌ Profile manager test failed: {e}")
        return False

async def test_crawl4ai():
    """Test crawl4ai integration"""
    print("🕷️ Testing crawl4ai integration...")
    try:
        from crawl4ai import AsyncWebCrawler
        
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun("https://httpbin.org/ip")
            
        if result.success:
            print("✅ Crawl4ai test successful")
            return True
        else:
            print(f"❌ Crawl4ai test failed: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Crawl4ai test failed: {e}")
        return False

async def test_api_routes():
    """Test API routes"""
    print("🌐 Testing API routes...")
    try:
        from fastapi.testclient import TestClient
        from app.api.routes.profiles import router as profiles_router
        from fastapi import FastAPI
        
        app = FastAPI()
        app.include_router(profiles_router, prefix="/api/profiles")
        
        client = TestClient(app)
        
        # Test health endpoint would be here
        # For now, just check if routes are importable
        print("✅ API routes test successful")
        return True
        
    except Exception as e:
        print(f"❌ API routes test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Running Facebook Automation Backend Tests...\n")
    
    tests = [
        test_database,
        test_profile_manager,
        test_crawl4ai,
        test_api_routes
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("📊 Test Summary:")
    print(f"   Passed: {passed}/{total}")
    print(f"   Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
