#!/bin/bash

# Facebook Automation Desktop - Test Real Facebook Login Workflow
# This script tests the complete real Facebook login workflow with cookie persistence

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${YELLOW}[STEP]${NC} $1"
}

# API base URL
API_BASE="http://127.0.0.1:8000/api"

echo "🔐 Testing Real Facebook Login Workflow with Cookie Persistence..."
echo ""

# Test 1: Create a test profile
print_step "1. Creating test profile for real Facebook login..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Real Facebook Login Test",
        "proxy_type": "no_proxy"
    }')

if echo "$CREATE_RESPONSE" | grep -q '"id"'; then
    PROFILE_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    print_success "Test profile created (ID: $PROFILE_ID)"
else
    print_error "Failed to create test profile"
    exit 1
fi

# Test 2: Get profile path
print_step "2. Getting profile path..."
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"profile_path"'; then
    PROFILE_PATH=$(echo "$STATUS_RESPONSE" | grep -o '"profile_path":"[^"]*"' | cut -d'"' -f4)
    print_success "Profile path: $PROFILE_PATH"
else
    print_error "Failed to get profile path"
    exit 1
fi

# Test 3: First Facebook open (no cookies)
print_step "3. Opening Facebook for the first time (no cookies)..."
FB1_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/open-facebook")
if echo "$FB1_RESPONSE" | grep -q '"success":true'; then
    print_success "Facebook opened successfully"
    
    if echo "$FB1_RESPONSE" | grep -q '"cookies_loaded":false'; then
        print_info "✓ No cookies loaded (expected for first time)"
    fi
    
    if echo "$FB1_RESPONSE" | grep -q '"logged_in":false'; then
        print_info "✓ Not logged in (expected for first time)"
    fi
    
    PAGE_URL=$(echo "$FB1_RESPONSE" | grep -o '"page_url":"[^"]*"' | cut -d'"' -f4)
    print_info "Facebook page opened at: $PAGE_URL"
else
    print_error "Failed to open Facebook"
    exit 1
fi

echo ""
print_warning "🚨 MANUAL ACTION REQUIRED:"
print_warning "1. A browser window should have opened with Facebook login page"
print_warning "2. Please login to Facebook manually in that browser"
print_warning "3. Complete the login process (including 2FA if required)"
print_warning "4. Once you're logged in and see Facebook homepage, press ENTER to continue..."
read -p "Press ENTER after you have successfully logged in to Facebook: "

# Test 4: Complete login to save cookies
print_step "4. Completing login to save cookies..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/complete-login")
if echo "$LOGIN_RESPONSE" | grep -q '"success":true'; then
    if echo "$LOGIN_RESPONSE" | grep -q '"logged_in":true'; then
        print_success "✓ Login completed successfully!"
        print_success "✓ Facebook cookies have been saved"
    else
        print_warning "Login not detected. Make sure you're logged in to Facebook."
        echo "Response: $LOGIN_RESPONSE"
    fi
else
    print_error "Failed to complete login"
    echo "Response: $LOGIN_RESPONSE"
    exit 1
fi

# Test 5: Check if cookies were saved
print_step "5. Checking if cookies were saved to file..."
COOKIES_FILE="$PROFILE_PATH/facebook_cookies.json"
if [ -f "$COOKIES_FILE" ]; then
    COOKIE_COUNT=$(cat "$COOKIES_FILE" | grep -o '"name"' | wc -l)
    print_success "✓ Facebook cookies saved: $COOKIE_COUNT cookies in file"
    print_info "Cookies file: $COOKIES_FILE"
else
    print_error "Facebook cookies file not found!"
    exit 1
fi

# Test 6: Close browser
print_step "6. Closing browser..."
CLOSE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser")
if echo "$CLOSE_RESPONSE" | grep -q '"success":true'; then
    print_success "✓ Browser closed successfully"
else
    print_error "Failed to close browser"
    exit 1
fi

# Test 7: Wait a moment and verify browser is closed
print_step "7. Verifying browser is closed..."
sleep 2
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"browser_active":false'; then
    print_success "✓ Browser is confirmed closed"
else
    print_error "Browser should be closed"
    exit 1
fi

# Test 8: The critical test - reopen Facebook with saved cookies
print_step "8. 🔥 CRITICAL TEST: Reopening Facebook with saved cookies..."
print_info "This should auto-launch browser and auto-login with saved cookies..."

FB2_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/open-facebook")
if echo "$FB2_RESPONSE" | grep -q '"success":true'; then
    print_success "✓ Facebook opened successfully after browser restart"
    
    # Check if cookies were loaded
    if echo "$FB2_RESPONSE" | grep -q '"cookies_loaded":true'; then
        print_success "✓ Saved cookies were loaded successfully!"
    else
        print_error "✗ Saved cookies were NOT loaded"
        echo "Response: $FB2_RESPONSE"
        exit 1
    fi
    
    # Check if auto-login worked
    if echo "$FB2_RESPONSE" | grep -q '"logged_in":true'; then
        print_success "🎉 AUTO-LOGIN SUCCESSFUL! You are logged in with saved cookies!"
    else
        print_warning "⚠️  Auto-login failed. Cookies may have expired or be invalid."
        print_info "This could happen if:"
        print_info "- Facebook session expired"
        print_info "- Cookies were not saved properly"
        print_info "- Facebook detected automation"
    fi
    
    PAGE_URL=$(echo "$FB2_RESPONSE" | grep -o '"page_url":"[^"]*"' | cut -d'"' -f4)
    print_info "Facebook page URL: $PAGE_URL"
else
    print_error "Failed to reopen Facebook"
    echo "Response: $FB2_RESPONSE"
    exit 1
fi

# Test 9: Final verification
print_step "9. Final verification..."
print_info "A browser window should now be open with Facebook"
print_info "If auto-login worked, you should see Facebook homepage without login form"
echo ""
read -p "Can you confirm Facebook is open and you're logged in? (y/n): " CONFIRM

if [[ "$CONFIRM" =~ ^[Yy]$ ]]; then
    print_success "🎉 COOKIE PERSISTENCE TEST PASSED!"
    print_success "✓ Facebook login workflow with cookie persistence is working!"
else
    print_warning "⚠️  Manual verification failed. Please check the browser."
fi

# Test 10: Cleanup
print_step "10. Cleaning up..."
curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser" > /dev/null

DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/profiles/$PROFILE_ID")
if echo "$DELETE_RESPONSE" | grep -q '"message":"Profile deleted successfully"'; then
    print_success "✓ Test profile deleted successfully"
else
    print_warning "Failed to delete test profile (manual cleanup may be needed)"
fi

echo ""
print_success "🎉 Real Facebook Login Test Completed!"
echo ""
print_info "Test Summary:"
print_info "✅ Profile creation and path detection"
print_info "✅ First Facebook login (manual)"
print_info "✅ Cookie saving after login"
print_info "✅ Browser close and restart"
print_info "✅ Cookie loading on restart"
print_info "✅ Auto-login with saved cookies"
print_info "✅ Profile cleanup"
echo ""
print_info "🚀 The Facebook automation workflow with cookie persistence is ready!"
print_info "Users can now:"
print_info "1. Open Facebook → Login manually → Complete login"
print_info "2. Close browser anytime"
print_info "3. Open Facebook again → Auto-login with saved cookies!"
