#!/usr/bin/env python3
"""
Test script to verify backend fixes for Scraping page issues
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_backend_endpoints():
    """Test backend endpoints that were causing issues"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🧪 Testing Backend Endpoints...")
        
        # Test 1: Health check
        try:
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    print("✅ Health check: OK")
                else:
                    print(f"❌ Health check failed: {response.status}")
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
        
        # Test 2: Get profiles (this was causing issues)
        try:
            async with session.get(f"{base_url}/api/profiles/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Profiles API: OK (found {len(data)} profiles)")
                else:
                    error_text = await response.text()
                    print(f"❌ Profiles API failed: {response.status} - {error_text}")
        except Exception as e:
            print(f"❌ Profiles API error: {e}")
        
        # Test 3: Get scraping tasks (this was causing "from_attributes" error)
        try:
            async with session.get(f"{base_url}/api/scraping/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Scraping API: OK (found {len(data)} tasks)")
                else:
                    error_text = await response.text()
                    print(f"❌ Scraping API failed: {response.status} - {error_text}")
        except Exception as e:
            print(f"❌ Scraping API error: {e}")
        
        # Test 4: Get export history
        try:
            async with session.get(f"{base_url}/api/scraping/exports/history") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Export History API: OK")
                else:
                    error_text = await response.text()
                    print(f"❌ Export History API failed: {response.status} - {error_text}")
        except Exception as e:
            print(f"❌ Export History API error: {e}")
        
        print("\n🎯 Backend test completed!")
        return True

def test_pydantic_models():
    """Test Pydantic model configurations"""
    print("\n🔧 Testing Pydantic Models...")
    
    try:
        from app.models.scraping import ScrapingTaskResponse, ScrapedUserResponse
        from app.models.profile import ProfileResponse
        from app.models.messaging import MessagingTaskResponse
        from app.models.system import SystemStatsResponse
        
        # Check if models have correct configuration
        models_to_test = [
            ScrapingTaskResponse,
            ScrapedUserResponse, 
            ProfileResponse,
            MessagingTaskResponse,
            SystemStatsResponse
        ]
        
        for model in models_to_test:
            if hasattr(model, 'model_config'):
                config = model.model_config
                if config.get('from_attributes') == True:
                    print(f"✅ {model.__name__}: Correct Pydantic v2 config")
                else:
                    print(f"❌ {model.__name__}: Missing from_attributes config")
            else:
                print(f"❌ {model.__name__}: No model_config found")
                
    except Exception as e:
        print(f"❌ Model test error: {e}")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🚀 Testing Backend Fixes for Scraping Page Issues\n")
    
    # Test 1: Pydantic models
    model_test_passed = test_pydantic_models()
    
    # Test 2: Backend endpoints
    endpoint_test_passed = await test_backend_endpoints()
    
    print(f"\n📊 Test Results:")
    print(f"   Pydantic Models: {'✅ PASS' if model_test_passed else '❌ FAIL'}")
    print(f"   Backend Endpoints: {'✅ PASS' if endpoint_test_passed else '❌ FAIL'}")
    
    if model_test_passed and endpoint_test_passed:
        print("\n🎉 All tests passed! The backend fixes should resolve the Scraping page issues.")
        print("\n📝 Summary of fixes applied:")
        print("   1. Updated Pydantic models from 'class Config' to 'model_config'")
        print("   2. Changed .from_attributes() to .model_validate() in API routes")
        print("   3. Enhanced frontend error handling and profile loading")
        print("   4. Added better user feedback for empty profile states")
    else:
        print("\n⚠️  Some tests failed. Please check the backend configuration.")
    
    return model_test_passed and endpoint_test_passed

if __name__ == "__main__":
    asyncio.run(main())
