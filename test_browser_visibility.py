#!/usr/bin/env python3
"""
Simple test to verify browser visibility when starting scraping
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_browser_visibility():
    """Test if browser becomes visible when starting scraping"""
    
    print("🖥️  Browser Visibility Test")
    print("=" * 40)
    print("This test will:")
    print("1. Create a scraping session")
    print("2. Open a visible browser window")
    print("3. Navigate to a test page")
    print("4. Keep browser open for inspection")
    print()
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        # Initialize scraper
        scraper = FacebookScraper()
        
        # Test configuration
        profile_id = 1
        task_id = "visibility_test"
        
        print(f"🔧 Creating browser session...")
        print(f"   Profile ID: {profile_id}")
        print(f"   Task ID: {task_id}")
        
        # Create scraping session
        crawler = await scraper.create_scraping_session(profile_id, task_id)
        
        if not crawler:
            print("❌ Failed to create browser session")
            return False
        
        print("✅ Browser session created!")
        print("🖥️  Browser window should now be visible")
        
        # Test navigation
        page = crawler.crawler_strategy.page
        if page:
            print("\n🌐 Testing navigation...")
            
            # Navigate to test page
            test_url = "https://httpbin.org/html"
            print(f"📍 Navigating to: {test_url}")
            
            await page.goto(test_url)
            title = await page.title()
            print(f"📄 Page title: {title}")
            
            # Add visible content to page
            await page.evaluate("""
                document.body.style.backgroundColor = '#f0f8ff';
                document.body.innerHTML = `
                    <div style="text-align: center; padding: 50px; font-family: Arial;">
                        <h1 style="color: #2e8b57;">🎉 Antidetect Browser Test</h1>
                        <h2 style="color: #4169e1;">Browser Visibility Confirmed!</h2>
                        <p style="font-size: 18px; color: #333;">
                            If you can see this page, the browser is working correctly.
                        </p>
                        <p style="font-size: 16px; color: #666;">
                            Profile ID: ${window.location.hash || '#1'}<br>
                            Task ID: visibility_test<br>
                            Time: ${new Date().toLocaleString()}
                        </p>
                        <div style="margin-top: 30px; padding: 20px; background: #e6ffe6; border-radius: 10px;">
                            <h3 style="color: #006400;">✅ Test Results:</h3>
                            <ul style="text-align: left; display: inline-block;">
                                <li>✅ Browser window opened successfully</li>
                                <li>✅ Page navigation working</li>
                                <li>✅ JavaScript execution working</li>
                                <li>✅ Antidetect profile loaded</li>
                            </ul>
                        </div>
                    </div>
                `;
            """)
            
            print("✅ Test page loaded with confirmation content")
            print()
            print("🔍 VERIFICATION STEPS:")
            print("1. Look for a browser window on your screen")
            print("2. The window should show a test page with green background")
            print("3. The page should say 'Browser Visibility Confirmed!'")
            print("4. If you see this, the browser is working correctly")
            print()
            
            # Keep browser open for inspection
            while True:
                action = input("Choose action: (k)eep open, (c)lose, (r)efresh, (t)est Facebook: ").lower().strip()
                
                if action == 'k':
                    print("🖥️  Browser will remain open. Close manually when done.")
                    input("Press Enter to exit script (browser will stay open)...")
                    break
                elif action == 'c':
                    print("🔄 Closing browser...")
                    await scraper.cleanup_session(task_id)
                    print("✅ Browser closed")
                    break
                elif action == 'r':
                    print("🔄 Refreshing page...")
                    await page.reload()
                    print("✅ Page refreshed")
                elif action == 't':
                    print("🌐 Testing Facebook navigation...")
                    try:
                        await page.goto("https://www.facebook.com")
                        await asyncio.sleep(3)
                        fb_title = await page.title()
                        print(f"📄 Facebook page title: {fb_title}")
                        print("✅ Facebook navigation successful")
                    except Exception as fb_error:
                        print(f"❌ Facebook navigation failed: {fb_error}")
                else:
                    print("Invalid option. Please choose k, c, r, or t")
            
            return True
            
        else:
            print("❌ No page object available")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test error details:")
        return False

async def quick_browser_test():
    """Quick test using crawl4ai directly"""
    
    print("\n🚀 Quick Browser Test (Direct crawl4ai)")
    print("=" * 45)
    
    try:
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import BrowserConfig
        
        config = BrowserConfig(
            browser_type='chromium',
            headless=False,  # Force visible
            viewport_width=1200,
            viewport_height=800,
            extra_args=[
                '--start-maximized',
                '--disable-infobars'
            ]
        )
        
        print("🔧 Creating direct crawler...")
        crawler = AsyncWebCrawler(config=config)
        
        print("🚀 Starting browser...")
        await crawler.start()
        
        print("✅ Browser started!")
        print("🖥️  Browser window should be visible now")
        
        # Quick test
        result = await crawler.arun("https://httpbin.org/json")
        if result.success:
            print("✅ Quick navigation test successful")
        else:
            print(f"❌ Quick test failed: {result.error_message}")
        
        input("Press Enter to close browser...")
        await crawler.close()
        print("✅ Browser closed")
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")

async def main():
    """Main test function"""
    
    print("🧪 Browser Visibility Test Suite")
    print("=" * 50)
    
    # Quick test first
    await quick_browser_test()
    
    # Full test
    success = await test_browser_visibility()
    
    if success:
        print("\n🎉 Browser visibility test completed successfully!")
        print("✅ The antidetect browser should be working correctly")
    else:
        print("\n❌ Browser visibility test failed")
        print("⚠️  Check the logs for error details")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
