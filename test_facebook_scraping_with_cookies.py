#!/usr/bin/env python3
"""
Test Facebook scraping with cookies (same as Open Facebook button)
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_facebook_scraping_with_cookies():
    """Test Facebook scraping using the same approach as Open Facebook button"""
    
    print("🍪 Facebook Scraping with Cookies Test")
    print("=" * 50)
    print("This test will:")
    print("1. Use ProfileManager to open Facebook (same as 'Open Facebook' button)")
    print("2. Load saved Facebook cookies automatically")
    print("3. Create scraping session with logged-in browser")
    print("4. Test navigation to Facebook post")
    print()
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile from database
        profile_id = int(input("Enter Profile ID to test (default: 1): ") or "1")
        
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile).where(Profile.id == profile_id))
            profile = result.scalar_one_or_none()
            
            if not profile:
                print(f"❌ Profile {profile_id} not found in database")
                return False
            
            print(f"✅ Found profile: {profile.name}")
            print(f"📁 Profile path: {profile.profile_path}")
            
            # Check if Facebook cookies exist
            cookies_file = Path(profile.profile_path) / 'facebook_cookies.json'
            if cookies_file.exists():
                print(f"🍪 Facebook cookies found: {cookies_file}")
                
                # Show cookie info
                import json
                try:
                    with open(cookies_file, 'r') as f:
                        cookies = json.load(f)
                    print(f"   📊 Number of cookies: {len(cookies)}")
                    
                    # Show some cookie names (not values for security)
                    cookie_names = [c.get('name', 'unknown') for c in cookies[:5]]
                    print(f"   🏷️  Sample cookies: {', '.join(cookie_names)}")
                    
                except Exception as e:
                    print(f"   ⚠️  Could not read cookies: {e}")
            else:
                print(f"⚠️  No Facebook cookies found at: {cookies_file}")
                print(f"   You may need to login to Facebook first using 'Open Facebook' button")
        
        # Initialize scraper
        scraper = FacebookScraper()
        
        print(f"\n🚀 Creating scraping session with Facebook cookies...")
        
        # Create scraping session (this will use ProfileManager.open_facebook_login)
        crawler = await scraper.create_scraping_session(profile_id, "cookie_test")
        
        if not crawler:
            print("❌ Failed to create scraping session")
            return False
        
        print("✅ Scraping session created successfully!")
        print("🖥️  Browser window should now be visible")
        print("🍪 Facebook cookies should be loaded automatically")
        
        # Test Facebook navigation
        print(f"\n🌐 Testing Facebook navigation...")
        
        # Get page from crawler
        if hasattr(crawler.crawler_strategy, 'get_page'):
            page = await crawler.crawler_strategy.get_page()
        else:
            page = crawler.crawler_strategy.page
        
        if page:
            print("✅ Page object available")
            
            # Check current URL
            current_url = page.url
            print(f"📍 Current URL: {current_url}")
            
            # Check if we're on Facebook
            if 'facebook.com' in current_url:
                print("✅ Already on Facebook!")
                
                # Check if logged in by looking for specific elements
                try:
                    # Wait a bit for page to load
                    await asyncio.sleep(3)
                    
                    # Check for login indicators
                    page_content = await page.content()
                    
                    if 'login' in current_url.lower() or 'login' in page_content.lower():
                        print("⚠️  Appears to be on login page - cookies may not be working")
                    else:
                        print("✅ Appears to be logged in!")
                        
                        # Try to get page title
                        title = await page.title()
                        print(f"📄 Page title: {title}")
                        
                except Exception as e:
                    print(f"⚠️  Could not check login status: {e}")
            else:
                print("ℹ️  Not on Facebook yet, this is normal")
            
            # Test navigation to a Facebook post
            test_post = input("\n📝 Enter Facebook post URL to test (or press Enter to skip): ").strip()
            
            if test_post:
                print(f"🌐 Navigating to: {test_post}")
                try:
                    await page.goto(test_post)
                    await asyncio.sleep(5)  # Wait for page load
                    
                    final_url = page.url
                    title = await page.title()
                    
                    print(f"✅ Navigation successful!")
                    print(f"📍 Final URL: {final_url}")
                    print(f"📄 Page title: {title}")
                    
                    # Check if we can see comments
                    page_content = await page.content()
                    if 'comment' in page_content.lower():
                        print("✅ Comments section detected on page")
                    else:
                        print("⚠️  No comments section detected")
                        
                except Exception as nav_error:
                    print(f"❌ Navigation failed: {nav_error}")
            
            # Keep browser open for inspection
            print(f"\n🔍 Browser Inspection Options:")
            print("1. Keep browser open for manual inspection")
            print("2. Test human-like scrolling")
            print("3. Close browser")
            
            choice = input("Choose option (1-3): ").strip()
            
            if choice == "1":
                print("🖥️  Browser will remain open for inspection")
                print("   You can manually check if Facebook is logged in")
                print("   Look for your profile picture, news feed, etc.")
                input("Press Enter when done inspecting...")
                
            elif choice == "2":
                print("🔄 Testing human-like scrolling...")
                try:
                    # Test scrolling
                    for i in range(3):
                        scroll_distance = 300 + i * 100
                        print(f"   📜 Scroll {i+1}: {scroll_distance}px")
                        await page.evaluate(f"window.scrollBy(0, {scroll_distance})")
                        await asyncio.sleep(2)
                    
                    print("✅ Scrolling test completed")
                    
                except Exception as scroll_error:
                    print(f"❌ Scrolling test failed: {scroll_error}")
                
                input("Press Enter to close browser...")
            
            # Cleanup will be handled by ProfileManager
            print("✅ Test completed successfully!")
            
        else:
            print("❌ No page object available")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test error details:")
        return False

async def main():
    """Main test function"""
    
    print("🧪 Facebook Scraping with Cookies Test Suite")
    print("=" * 60)
    
    success = await test_facebook_scraping_with_cookies()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("✅ Facebook scraping with cookies should be working")
        print("\n📝 What this test verified:")
        print("   - Profile database lookup")
        print("   - Facebook cookie loading")
        print("   - Browser session creation")
        print("   - Facebook navigation")
        print("   - Login status detection")
    else:
        print("\n❌ Test failed")
        print("⚠️  Check the logs for error details")
        print("\n🔧 Troubleshooting tips:")
        print("   - Make sure profile exists in database")
        print("   - Use 'Open Facebook' button first to login")
        print("   - Check if Facebook cookies are saved")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
