#!/usr/bin/env python3
"""
Test Facebook modal scraping with enhanced scrolling
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,  # Use DEBUG to see detailed scrolling logs
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_modal_scraping():
    """Test Facebook modal scraping with enhanced scrolling"""
    
    print("🎯 Facebook Modal Scraping Test")
    print("=" * 40)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if not profiles:
                print("❌ No profiles found")
                return False
            
            test_profile = profiles[0]
            print(f"✅ Using profile: {test_profile.name}")
            
            # Create scraper
            scraper = FacebookScraper()
            
            # Test URL
            test_url = input("📝 Enter Facebook post URL (should open in modal): ").strip()
            if not test_url:
                print("❌ No URL provided")
                return False
            
            print(f"\n🔧 Step 1: Creating scraping session...")
            
            # Create session
            crawler = await scraper.create_scraping_session(test_profile.id, "modal_test")
            
            if not crawler:
                print("❌ Failed to create session")
                return False
            
            print("✅ Session created successfully")
            
            print(f"\n🌐 Step 2: Testing modal detection and scrolling...")
            
            try:
                # Test modal scraping with enhanced logging
                results = await scraper.scrape_facebook_post(
                    crawler=crawler,
                    post_url=test_url,
                    scraping_types=[ScrapingType.COMMENTS],
                    max_results=10  # Test with 10 comments
                )
                
                print("✅ scrape_facebook_post completed!")
                
                comments = results.get('comments', [])
                print(f"📊 Results: {len(comments)} comments found")
                
                if len(comments) > 0:
                    print("\n📝 Sample Comments:")
                    for i, comment in enumerate(comments[:3]):  # Show first 3
                        print(f"   {i+1}. {comment.get('full_name', 'Unknown')}: {comment.get('interaction_content', '')[:100]}...")
                    
                    print("\n🎉 SUCCESS!")
                    print("✅ Modal detection working")
                    print("✅ Scrolling within modal working")
                    print("✅ Comment extraction working")
                    success = True
                    
                else:
                    print("\n⚠️  No comments found")
                    print("🔧 This could mean:")
                    print("   - Post has no comments")
                    print("   - Modal not detected properly")
                    print("   - Need to login to Facebook first")
                    print("   - Comments not loaded yet")
                    
                    # Manual verification
                    manual_check = input("\nDid you see scrolling activity in the browser? (y/N): ").lower().strip()
                    if manual_check == 'y':
                        print("✅ Scrolling is working - post may just have no comments")
                        success = True
                    else:
                        print("❌ Scrolling may not be working properly")
                        success = False
                
            except Exception as scrape_error:
                error_msg = str(scrape_error)
                
                if "not logged in" in error_msg.lower():
                    print("❌ LOGIN REQUIRED")
                    print("🔧 Please login to Facebook first via Profile Manager")
                    success = False
                else:
                    print(f"❌ Scraping error: {scrape_error}")
                    success = False
            
            # Cleanup
            try:
                await scraper.cleanup_session("modal_test")
                print("✅ Session cleanup completed")
            except:
                pass
            
            return success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Modal scraping test error:")
        return False

def print_modal_instructions():
    """Print instructions for modal scraping"""
    
    print("\n📋 Facebook Modal Scraping Guide")
    print("=" * 40)
    
    print("🎯 WHAT THIS TEST DOES:")
    print("   ✅ Detects Facebook modal containers")
    print("   ✅ Scrolls within modal (not window)")
    print("   ✅ Extracts comments from modal")
    print("   ✅ Uses XPath: //*[@id=\"mount_0_0_/I\"]/div/div[1]/div/div[5]/div/div/div[2]/div/div/div/div")
    
    print("\n🔧 ENHANCED FEATURES:")
    print("   - Modal container detection")
    print("   - XPath-based scrolling")
    print("   - Fallback to CSS selectors")
    print("   - Comment extraction from modal")
    print("   - Duplicate filtering")
    
    print("\n📝 EXPECTED BEHAVIOR:")
    print("   1. Browser opens to Facebook post")
    print("   2. Post opens in modal overlay")
    print("   3. Scrolling happens within modal")
    print("   4. Comments load progressively")
    print("   5. Data extracted and saved")
    
    print("\n✅ SUCCESS INDICATORS:")
    print("   - See scrolling activity in modal")
    print("   - Comments count increases")
    print("   - Data extracted successfully")
    print("   - Debug logs show modal detection")
    
    print("\n❌ TROUBLESHOOTING:")
    print("   - If no scrolling: Check modal XPath")
    print("   - If no comments: Verify login status")
    print("   - If errors: Check browser console")

async def main():
    """Main test function"""
    
    print("🧪 Facebook Modal Scraping Test Suite")
    print("=" * 50)
    
    print_modal_instructions()
    
    print(f"\n🚀 Starting Modal Test...")
    success = await test_modal_scraping()
    
    print(f"\n📊 Modal Test Results")
    print("=" * 25)
    
    if success:
        print("🎉 MODAL SCRAPING SUCCESS!")
        print("✅ Modal detection working")
        print("✅ Scrolling within modal working")
        print("✅ Comment extraction working")
        print("✅ Enhanced Facebook scraping ready!")
        
    else:
        print("⚠️  MODAL SCRAPING ISSUES")
        print("🔧 Check the error messages above")
        print("💡 Make sure you're logged in to Facebook")
        print("🔍 Verify the post URL opens in modal")
    
    print(f"\n🎯 TECHNICAL SUMMARY:")
    print("✅ Enhanced _perform_natural_scroll for modal")
    print("✅ Enhanced _count_comments for modal")
    print("✅ Enhanced _extract_comments_batch for modal")
    print("✅ XPath-based container detection")
    print("✅ Fallback mechanisms implemented")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
