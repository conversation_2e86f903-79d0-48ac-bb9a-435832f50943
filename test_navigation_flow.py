#!/usr/bin/env python3
"""
Test navigation flow to ensure browser goes to Facebook post URL
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_navigation_flow():
    """Test complete navigation flow from Facebook homepage to post URL"""
    
    print("🧪 Navigation Flow Test")
    print("=" * 30)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if not profiles:
                print("❌ No profiles found. Run fix_scraping_complete.py first")
                return False
            
            test_profile = profiles[0]
            print(f"✅ Using profile: {test_profile.name} (ID: {test_profile.id})")
            
            # Create scraper
            scraper = FacebookScraper()
            
            # Test Facebook post URL (use a real one for testing)
            test_post_url = input("📝 Enter Facebook post URL to test (or press Enter for demo): ").strip()
            if not test_post_url:
                test_post_url = "https://www.facebook.com/groups/123456789/posts/987654321"
                print(f"📝 Using demo URL: {test_post_url}")
            
            print(f"\n🔧 Step 1: Creating scraping session...")
            
            # Create session
            crawler = await scraper.create_scraping_session(test_profile.id, "nav_test")
            
            if not crawler:
                print("❌ Failed to create scraping session")
                return False
            
            print("✅ Scraping session created")
            
            # Check initial state
            if hasattr(crawler.crawler_strategy, 'get_page'):
                page = await crawler.crawler_strategy.get_page()
                if page:
                    initial_url = page.url
                    print(f"📍 Initial URL: {initial_url}")
                    
                    print(f"\n🔧 Step 2: Testing direct navigation...")
                    
                    # Test direct navigation
                    await page.goto(test_post_url, wait_until='networkidle')
                    await asyncio.sleep(3)
                    
                    nav_url = page.url
                    print(f"📍 After navigation: {nav_url}")
                    
                    # Verify navigation
                    if test_post_url in nav_url or any(keyword in nav_url for keyword in ['posts', 'story', 'permalink']):
                        print("✅ Direct navigation successful")
                    else:
                        print(f"⚠️  Direct navigation may have issues")
                        print(f"   Expected: {test_post_url}")
                        print(f"   Got: {nav_url}")
                    
                    print(f"\n🔧 Step 3: Testing scrape_facebook_post method...")
                    
                    # Test full scraping method
                    try:
                        results = await scraper.scrape_facebook_post(
                            crawler=crawler,
                            post_url=test_post_url,
                            scraping_types=[ScrapingType.COMMENTS],
                            max_results=5
                        )
                        
                        print("✅ scrape_facebook_post completed")
                        print(f"📊 Results: {len(results.get('comments', []))} comments found")
                        
                        # Check final URL
                        final_url = page.url
                        print(f"📍 Final URL: {final_url}")
                        
                        if test_post_url in final_url or any(keyword in final_url for keyword in ['posts', 'story', 'permalink']):
                            print("✅ Final URL is correct")
                            navigation_success = True
                        else:
                            print("⚠️  Final URL may not be correct")
                            print(f"   Expected: {test_post_url}")
                            print(f"   Got: {final_url}")
                            navigation_success = False
                            
                    except Exception as scrape_error:
                        print(f"❌ scrape_facebook_post failed: {scrape_error}")
                        navigation_success = False
                    
                    # Keep browser open for manual verification
                    print(f"\n🔍 Manual Verification")
                    print("=" * 25)
                    print("🖥️  Browser window should be open")
                    print(f"📍 Current URL: {page.url}")
                    print("👀 Please check if the browser is showing the correct Facebook post")
                    
                    manual_check = input("\nIs the browser showing the correct Facebook post? (y/N): ").lower().strip()
                    if manual_check == 'y':
                        print("✅ Manual verification: SUCCESS")
                        manual_success = True
                    else:
                        print("❌ Manual verification: FAILED")
                        manual_success = False
                    
                    # Cleanup
                    await scraper.cleanup_session("nav_test")
                    print("✅ Session cleanup completed")
                    
                    return navigation_success and manual_success
                    
                else:
                    print("❌ No page object available")
                    return False
            else:
                print("❌ Cannot access page from crawler")
                return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Navigation test error:")
        return False

async def test_scraping_task_manager_flow():
    """Test navigation through ScrapingTaskManager"""
    
    print(f"\n🔧 ScrapingTaskManager Navigation Test")
    print("=" * 40)
    
    try:
        from app.services.scraping_task_manager import ScrapingTaskManager
        from app.models.scraping import ScrapingConfig, ScrapingType
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if not profiles:
                print("❌ No profiles found")
                return False
            
            test_profile = profiles[0]
            
            # Test post URL
            test_post_url = input("📝 Enter Facebook post URL for TaskManager test: ").strip()
            if not test_post_url:
                test_post_url = "https://www.facebook.com/groups/123456789/posts/987654321"
            
            # Create task manager
            task_manager = ScrapingTaskManager()
            
            # Create config
            config = ScrapingConfig(
                profile_id=test_profile.id,
                target_url=test_post_url,
                scraping_types=[ScrapingType.COMMENTS],
                max_results=5
            )
            
            print(f"🔧 Creating scraping task...")
            print(f"   Profile: {test_profile.id}")
            print(f"   URL: {test_post_url}")
            
            # Create task
            task_id = await task_manager.create_scraping_task(config)
            print(f"✅ Task created: {task_id}")
            
            # Note: We won't start the task here as it would run in background
            # This test is just to verify task creation works
            
            print("✅ ScrapingTaskManager navigation test completed")
            print("   Task created successfully with target URL")
            
            return True
            
    except Exception as e:
        print(f"❌ TaskManager test failed: {e}")
        return False

async def main():
    """Main test function"""
    
    print("🧪 Facebook Post Navigation Test Suite")
    print("=" * 50)
    
    # Test direct navigation
    nav_success = await test_navigation_flow()
    
    # Test task manager
    task_success = await test_scraping_task_manager_flow()
    
    print(f"\n📊 Navigation Test Results")
    print("=" * 30)
    
    if nav_success:
        print("✅ Direct navigation: WORKING")
    else:
        print("❌ Direct navigation: FAILED")
    
    if task_success:
        print("✅ TaskManager integration: WORKING")
    else:
        print("❌ TaskManager integration: FAILED")
    
    if nav_success and task_success:
        print("\n🎉 SUCCESS!")
        print("✅ Navigation flow is working correctly")
        print("✅ Browser will navigate to Facebook post URL")
        print("✅ Scraping will work on the correct page")
        
        print(f"\n📝 What was fixed:")
        print("   - Enhanced navigation in scrape_facebook_post")
        print("   - Proper URL verification")
        print("   - Avoided duplicate navigation")
        print("   - Added manual verification step")
        
    else:
        print("\n❌ ISSUES DETECTED")
        print("🔧 Navigation flow needs more work")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
