#!/usr/bin/env python3
"""
Simple test for navigation flow
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_simple_navigation():
    """Simple navigation test"""
    
    print("🧪 Simple Navigation Test")
    print("=" * 30)
    
    try:
        # Import with fresh modules
        import importlib
        import sys
        
        # Clear module cache
        if 'app.services.facebook_scraper' in sys.modules:
            del sys.modules['app.services.facebook_scraper']
        
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if not profiles:
                print("❌ No profiles found")
                return False
            
            test_profile = profiles[0]
            print(f"✅ Using profile: {test_profile.name}")
            
            # Create scraper
            scraper = FacebookScraper()
            
            # Test URL
            test_url = "https://www.facebook.com/groups/testgroup/posts/123456789"
            print(f"📝 Test URL: {test_url}")
            
            print(f"\n🔧 Creating scraping session...")
            
            # Create session
            crawler = await scraper.create_scraping_session(test_profile.id, "simple_test")
            
            if not crawler:
                print("❌ Failed to create session")
                return False
            
            print("✅ Session created")
            print(f"   Type: {type(crawler).__name__}")
            
            # Test navigation using scrape_facebook_post
            print(f"\n🌐 Testing navigation via scrape_facebook_post...")
            
            try:
                results = await scraper.scrape_facebook_post(
                    crawler=crawler,
                    post_url=test_url,
                    scraping_types=[ScrapingType.COMMENTS],
                    max_results=3
                )
                
                print("✅ scrape_facebook_post completed")
                print(f"📊 Results: {len(results.get('comments', []))} comments")
                
                # Manual verification
                print(f"\n🔍 Manual Verification Required")
                print("=" * 35)
                print("🖥️  Check the browser window:")
                print(f"   Expected URL: {test_url}")
                print("   Should show Facebook post page")
                print("   Should NOT be on Facebook homepage")
                
                manual_ok = input("\nIs browser showing the correct Facebook post? (y/N): ").lower().strip()
                
                if manual_ok == 'y':
                    print("✅ Manual verification: SUCCESS")
                    success = True
                else:
                    print("❌ Manual verification: FAILED")
                    print("   Navigation did not work correctly")
                    success = False
                
            except Exception as scrape_error:
                print(f"❌ scrape_facebook_post failed: {scrape_error}")
                success = False
            
            # Cleanup
            try:
                await scraper.cleanup_session("simple_test")
                print("✅ Cleanup completed")
            except:
                pass
            
            return success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Simple navigation test error:")
        return False

async def main():
    """Main test function"""
    
    print("🧪 Simple Navigation Test Suite")
    print("=" * 40)
    
    success = await test_simple_navigation()
    
    print(f"\n📊 Test Results")
    print("=" * 15)
    
    if success:
        print("🎉 SUCCESS!")
        print("✅ Navigation flow is working")
        print("✅ Browser navigates to Facebook post URL")
        print("✅ scrape_facebook_post method works")
        
        print(f"\n📝 What this confirms:")
        print("   - Browser session creation works")
        print("   - Navigation to post URL works")
        print("   - Scraping method executes")
        print("   - Manual verification passed")
        
    else:
        print("❌ FAILED!")
        print("🔧 Navigation flow needs fixing")
        
        print(f"\n🔧 Possible issues:")
        print("   - Browser not navigating to post URL")
        print("   - Staying on Facebook homepage")
        print("   - Navigation method not working")
        print("   - Page object access issues")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
