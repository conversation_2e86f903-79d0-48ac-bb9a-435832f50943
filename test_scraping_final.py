#!/usr/bin/env python3
"""
Final test to verify scraping system works
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_scraping_session_creation():
    """Test scraping session creation with fallback"""
    
    print("🧪 Final Scraping Test")
    print("=" * 30)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Get profile
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if not profiles:
                print("❌ No profiles found. Run fix_scraping_complete.py first")
                return False
            
            test_profile = profiles[0]
            print(f"✅ Using profile: {test_profile.name} (ID: {test_profile.id})")
            
            # Test scraper creation
            scraper = FacebookScraper()
            print("✅ FacebookScraper created")
            
            # Test session creation
            print("🔧 Testing scraping session creation...")
            
            try:
                # This should use fallback method since ProfileManager might not work
                crawler = await scraper.create_scraping_session(test_profile.id, "final_test")
                
                if crawler:
                    print("✅ Scraping session created successfully!")
                    print("   - Session type:", type(crawler).__name__)
                    
                    # Test if it's our wrapper or fallback
                    if hasattr(crawler, 'crawler_strategy'):
                        print("   - Using CrawlerWrapper (ProfileManager integration)")
                    else:
                        print("   - Using fallback AsyncWebCrawler")
                    
                    # Test cleanup
                    await scraper.cleanup_session("final_test")
                    print("✅ Session cleanup successful")
                    
                    return True
                    
                else:
                    print("❌ Scraping session creation returned None")
                    return False
                    
            except Exception as session_error:
                print(f"❌ Session creation failed: {session_error}")
                return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def test_web_interface_integration():
    """Test web interface integration"""
    
    print("\n🌐 Web Interface Integration Test")
    print("=" * 40)
    
    try:
        import requests
        
        # Test backend health
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ Backend is running")
                
                # Test profiles API
                profiles_response = requests.get("http://localhost:8000/api/profiles/", timeout=5)
                if profiles_response.status_code == 200:
                    profiles_data = profiles_response.json()
                    print(f"✅ Profiles API working - {len(profiles_data)} profiles found")
                    
                    # Test scraping API
                    scraping_response = requests.get("http://localhost:8000/api/scraping/", timeout=5)
                    if scraping_response.status_code == 200:
                        print("✅ Scraping API accessible")
                        return True
                    else:
                        print(f"⚠️  Scraping API returned: {scraping_response.status_code}")
                        return False
                        
                else:
                    print(f"⚠️  Profiles API returned: {profiles_response.status_code}")
                    return False
                    
            else:
                print(f"⚠️  Backend health check failed: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException:
            print("⚠️  Backend not running. Start with: ./scripts/dev-web.sh")
            return False
            
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        return False

def print_usage_guide():
    """Print usage guide"""
    
    print("\n📝 Usage Guide")
    print("=" * 20)
    print("✅ System is now ready for use!")
    print()
    print("🚀 To start scraping:")
    print("1. Start the system:")
    print("   ./scripts/dev-web.sh")
    print()
    print("2. Access web interface:")
    print("   http://localhost:3000")
    print()
    print("3. Login to Facebook (first time):")
    print("   - Go to Profile Manager")
    print("   - Click 'Open Facebook' button")
    print("   - Login manually in browser")
    print("   - Cookies will be saved automatically")
    print()
    print("4. Start scraping:")
    print("   - Go to Scraping page")
    print("   - Enter Facebook post URL")
    print("   - Select profile")
    print("   - Click 'Start Scraping'")
    print("   - Browser will open with Facebook cookies")
    print("   - Scraping will proceed automatically")
    print()
    print("🎯 Expected behavior:")
    print("   ✅ Browser opens immediately")
    print("   ✅ Facebook is already logged in")
    print("   ✅ Human-like scrolling and extraction")
    print("   ✅ Data saved to database")

async def main():
    """Main test function"""
    
    print("🎉 Final Scraping System Test")
    print("=" * 50)
    
    # Test session creation
    session_success = await test_scraping_session_creation()
    
    # Test web interface
    web_success = await test_web_interface_integration()
    
    print(f"\n📊 Final Test Results")
    print("=" * 25)
    
    if session_success:
        print("✅ Scraping session creation: WORKING")
    else:
        print("❌ Scraping session creation: FAILED")
    
    if web_success:
        print("✅ Web interface integration: WORKING")
    else:
        print("⚠️  Web interface: NOT RUNNING (start with ./scripts/dev-web.sh)")
    
    if session_success:
        print("\n🎉 SUCCESS!")
        print("✅ Scraping system is fully functional")
        print("✅ Browser will open with Facebook cookies")
        print("✅ Ready for production use")
        
        print_usage_guide()
        
    else:
        print("\n❌ ISSUES DETECTED")
        print("🔧 Scraping session creation failed")
        print("   Check the error messages above")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
