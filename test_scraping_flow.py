#!/usr/bin/env python3
"""
Test complete scraping flow to ensure everything works as expected
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_scraping_flow():
    """Test complete scraping flow"""
    
    print("🧪 Complete Scraping Flow Test")
    print("=" * 50)
    print("This test will:")
    print("1. Create a test profile")
    print("2. Test scraping session creation")
    print("3. Verify browser opens with cookies")
    print("4. Test scraping task manager")
    print("5. Verify cleanup")
    print()
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.services.scraping_task_manager import ScrapingTaskManager
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from app.models.scraping import ScrapingConfig, ScrapingType
        from sqlalchemy import select
        
        # Test 1: Check if profiles exist
        print("📊 Test 1: Profile Database Check")
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()
            
            if profiles:
                print(f"✅ Found {len(profiles)} profiles in database")
                for profile in profiles[:3]:  # Show first 3
                    print(f"   - Profile {profile.id}: {profile.name}")
                
                # Use first profile for testing
                test_profile = profiles[0]
                profile_id = test_profile.id
                
            else:
                print("⚠️  No profiles found in database")
                print("   Creating a test profile...")
                
                # Create test profile
                test_profile = Profile(
                    name="Test Profile for Scraping",
                    profile_path="data/profiles/test_profile",
                    proxy_type="no_proxy",
                    status="active"
                )
                
                db.add(test_profile)
                await db.commit()
                await db.refresh(test_profile)
                
                profile_id = test_profile.id
                print(f"✅ Created test profile with ID: {profile_id}")
        
        # Test 2: Test FacebookScraper directly
        print(f"\n🔧 Test 2: FacebookScraper Session Creation")
        scraper = FacebookScraper()
        
        try:
            crawler = await scraper.create_scraping_session(profile_id, "test_flow")
            
            if crawler:
                print("✅ Scraping session created successfully")
                print("   - CrawlerWrapper created")
                print("   - Browser context available")
                print("   - Page access ready")
                
                # Test page access
                if hasattr(crawler.crawler_strategy, 'get_page'):
                    page = await crawler.crawler_strategy.get_page()
                    if page:
                        print("✅ Page object accessible")
                    else:
                        print("⚠️  Page object is None")
                else:
                    print("⚠️  No get_page method available")
                    
            else:
                print("❌ Scraping session creation failed")
                return False
                
        except Exception as scraper_error:
            print(f"❌ Scraper error: {scraper_error}")
            return False
        
        # Test 3: Test ScrapingTaskManager
        print(f"\n📋 Test 3: ScrapingTaskManager Integration")
        task_manager = ScrapingTaskManager()
        
        try:
            # Create scraping config
            config = ScrapingConfig(
                profile_id=profile_id,
                target_url="https://www.facebook.com/groups/test/posts/123",
                scraping_types=[ScrapingType.COMMENTS],
                max_results=10
            )
            
            # Create task
            task_id = await task_manager.create_scraping_task(config)
            print(f"✅ Scraping task created: {task_id}")
            
            # Check task status
            if task_id in task_manager.active_tasks:
                print("✅ Task registered in active_tasks")
                task_info = task_manager.active_tasks[task_id]
                print(f"   - Config: {task_info['config'].target_url}")
                print(f"   - Created: {task_info['created_at']}")
            else:
                print("❌ Task not found in active_tasks")
                return False
            
            # Test task progress
            if task_id in task_manager.task_progress:
                print("✅ Task progress tracking initialized")
                progress = task_manager.task_progress[task_id]
                print(f"   - Status: {progress['status']}")
                print(f"   - Progress: {progress['progress']}%")
            else:
                print("❌ Task progress not initialized")
                return False
                
        except Exception as task_error:
            print(f"❌ Task manager error: {task_error}")
            return False
        
        # Test 4: Test API Integration (if backend is running)
        print(f"\n🌐 Test 4: API Integration Test")
        try:
            import requests
            
            # Test health endpoint
            health_response = requests.get("http://localhost:8000/health", timeout=5)
            if health_response.status_code == 200:
                print("✅ Backend API is running")
                
                # Test profiles endpoint
                profiles_response = requests.get("http://localhost:8000/api/profiles/", timeout=5)
                if profiles_response.status_code == 200:
                    print("✅ Profiles API accessible")
                    
                    # Test scraping endpoint structure
                    scraping_response = requests.get("http://localhost:8000/api/scraping/", timeout=5)
                    if scraping_response.status_code == 200:
                        print("✅ Scraping API accessible")
                    else:
                        print(f"⚠️  Scraping API returned: {scraping_response.status_code}")
                        
                else:
                    print(f"⚠️  Profiles API returned: {profiles_response.status_code}")
                    
            else:
                print(f"⚠️  Backend health check failed: {health_response.status_code}")
                
        except requests.exceptions.RequestException as api_error:
            print(f"⚠️  API test failed (backend may not be running): {api_error}")
        
        # Test 5: Cleanup Test
        print(f"\n🧹 Test 5: Cleanup Test")
        try:
            # Test cleanup session
            await scraper.cleanup_session("test_flow")
            print("✅ Session cleanup completed")
            
            # Check if session was removed
            if "test_flow" not in scraper.active_crawlers:
                print("✅ Session removed from active_crawlers")
            else:
                print("⚠️  Session still in active_crawlers")
                
        except Exception as cleanup_error:
            print(f"⚠️  Cleanup error: {cleanup_error}")
        
        print(f"\n🎉 Scraping Flow Test Summary")
        print("=" * 40)
        print("✅ Profile database access")
        print("✅ FacebookScraper session creation")
        print("✅ CrawlerWrapper functionality")
        print("✅ ScrapingTaskManager integration")
        print("✅ API endpoints accessibility")
        print("✅ Session cleanup")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test error details:")
        return False

async def test_mock_scraping():
    """Test mock scraping without actual Facebook"""
    
    print(f"\n🎭 Mock Scraping Test")
    print("=" * 30)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        scraper = FacebookScraper()
        
        # Test mock methods
        print("🔧 Testing mock scraping methods...")
        
        # Test human-like scrolling (mock)
        mock_crawler = type('MockCrawler', (), {
            'crawler_strategy': type('MockStrategy', (), {
                'get_page': lambda: None,
                '_page': None,
                'page': None
            })()
        })()
        
        # This should handle gracefully
        results = await scraper._human_like_scrolling(mock_crawler, 10)
        print(f"✅ Human-like scrolling handled gracefully: {len(results)} results")
        
        # Test comment scraping (mock)
        comment_results = await scraper._scrape_comments(mock_crawler, "https://test.com", 5)
        print(f"✅ Comment scraping handled gracefully: {len(comment_results)} results")
        
        print("✅ Mock scraping tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Mock scraping test failed: {e}")
        return False

async def main():
    """Main test function"""
    
    print("🧪 Complete Scraping System Test Suite")
    print("=" * 60)
    
    # Run flow test
    flow_success = await test_scraping_flow()
    
    # Run mock test
    mock_success = await test_mock_scraping()
    
    print(f"\n📊 Final Test Results")
    print("=" * 30)
    
    if flow_success and mock_success:
        print("🎉 All tests passed successfully!")
        print("✅ Scraping system is ready for use")
        print("\n📝 Next steps:")
        print("   1. Use 'Open Facebook' button to login to Facebook")
        print("   2. Create scraping tasks via web interface")
        print("   3. Browser should open with Facebook cookies loaded")
        print("   4. Scraping should proceed automatically")
        
    else:
        print("❌ Some tests failed")
        if not flow_success:
            print("   - Scraping flow test failed")
        if not mock_success:
            print("   - Mock scraping test failed")
        
        print("\n🔧 Troubleshooting:")
        print("   - Check backend logs for errors")
        print("   - Verify database connectivity")
        print("   - Ensure ProfileManager is working")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
