#!/usr/bin/env python3
"""
Simple test to verify scraping system works
"""

import asyncio
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_simple_scraping():
    """Simple test without complex dependencies"""
    
    print("🧪 Simple Scraping System Test")
    print("=" * 40)
    
    try:
        # Test 1: Import modules
        print("📦 Test 1: Module Imports")
        try:
            from app.services.facebook_scraper import FacebookScraper
            print("✅ FacebookScraper imported successfully")
            
            from app.services.scraping_task_manager import ScrapingTaskManager
            print("✅ ScrapingTaskManager imported successfully")
            
            from app.models.scraping import ScrapingConfig, ScrapingType
            print("✅ Scraping models imported successfully")
            
        except Exception as import_error:
            print(f"❌ Import failed: {import_error}")
            return False
        
        # Test 2: Create instances
        print("\n🔧 Test 2: Instance Creation")
        try:
            scraper = FacebookScraper()
            print("✅ FacebookScraper instance created")
            
            task_manager = ScrapingTaskManager()
            print("✅ ScrapingTaskManager instance created")
            
        except Exception as instance_error:
            print(f"❌ Instance creation failed: {instance_error}")
            return False
        
        # Test 3: Test basic functionality
        print("\n⚙️  Test 3: Basic Functionality")
        try:
            # Test proxy URL building
            proxy_config = {
                'type': 'http',
                'host': 'localhost',
                'port': 8080
            }
            proxy_url = scraper._build_proxy_url(proxy_config)
            print(f"✅ Proxy URL building works: {proxy_url}")
            
            # Test scraping config creation
            config = ScrapingConfig(
                profile_id=1,
                target_url="https://facebook.com/test",
                scraping_types=[ScrapingType.COMMENTS],
                max_results=10
            )
            print("✅ ScrapingConfig creation works")
            
        except Exception as func_error:
            print(f"❌ Basic functionality failed: {func_error}")
            return False
        
        # Test 4: Test API endpoints (if backend running)
        print("\n🌐 Test 4: API Connectivity")
        try:
            import requests
            
            # Test health endpoint
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ Backend API is accessible")
                
                # Test profiles endpoint
                profiles_response = requests.get("http://localhost:8000/api/profiles/", timeout=5)
                if profiles_response.status_code == 200:
                    print("✅ Profiles API works")
                else:
                    print(f"⚠️  Profiles API returned: {profiles_response.status_code}")
                
            else:
                print(f"⚠️  Backend health check failed: {response.status_code}")
                
        except Exception as api_error:
            print(f"⚠️  API test failed (backend may not be running): {api_error}")
        
        print("\n🎉 Simple Test Results")
        print("=" * 30)
        print("✅ All core modules import successfully")
        print("✅ All instances create without errors")
        print("✅ Basic functionality works")
        print("✅ System is ready for scraping")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def test_web_interface():
    """Test if web interface is accessible"""
    
    print("\n🌐 Web Interface Test")
    print("=" * 25)
    
    try:
        import requests
        
        # Test frontend
        try:
            frontend_response = requests.get("http://localhost:3000", timeout=5)
            if frontend_response.status_code == 200:
                print("✅ Frontend is accessible at http://localhost:3000")
            else:
                print(f"⚠️  Frontend returned: {frontend_response.status_code}")
        except:
            print("⚠️  Frontend not accessible (may not be running)")
        
        # Test backend docs
        try:
            docs_response = requests.get("http://localhost:8000/docs", timeout=5)
            if docs_response.status_code == 200:
                print("✅ API docs accessible at http://localhost:8000/docs")
            else:
                print(f"⚠️  API docs returned: {docs_response.status_code}")
        except:
            print("⚠️  API docs not accessible")
            
        return True
        
    except Exception as e:
        print(f"⚠️  Web interface test failed: {e}")
        return False

def print_usage_instructions():
    """Print usage instructions"""
    
    print("\n📝 Usage Instructions")
    print("=" * 30)
    print("1. 🚀 Start the system:")
    print("   ./scripts/dev-web.sh")
    print()
    print("2. 🌐 Access web interface:")
    print("   Frontend: http://localhost:3000")
    print("   API Docs: http://localhost:8000/docs")
    print()
    print("3. 👤 Create a profile:")
    print("   - Go to Profile Manager")
    print("   - Click 'Create Profile'")
    print("   - Fill in profile details")
    print()
    print("4. 🍪 Login to Facebook:")
    print("   - Click 'Open Facebook' button")
    print("   - Login manually in browser")
    print("   - Cookies will be saved automatically")
    print()
    print("5. 📊 Start scraping:")
    print("   - Go to Scraping page")
    print("   - Enter Facebook post URL")
    print("   - Select profile")
    print("   - Click 'Start Scraping'")
    print("   - Browser will open with Facebook cookies")
    print()
    print("🎯 Expected behavior:")
    print("   ✅ Browser opens immediately")
    print("   ✅ Facebook is already logged in")
    print("   ✅ Scraping proceeds automatically")
    print("   ✅ Human-like scrolling and extraction")

async def main():
    """Main test function"""
    
    print("🧪 Scraping System Verification")
    print("=" * 50)
    
    # Run simple test
    simple_success = await test_simple_scraping()
    
    # Test web interface
    web_success = await test_web_interface()
    
    print(f"\n📊 Final Results")
    print("=" * 20)
    
    if simple_success:
        print("🎉 Core system test passed!")
        print("✅ All modules and functionality work correctly")
        
        if web_success:
            print("✅ Web interface is accessible")
        else:
            print("⚠️  Web interface may not be running")
            print("   Run: ./scripts/dev-web.sh")
        
        print_usage_instructions()
        
    else:
        print("❌ Core system test failed")
        print("🔧 Check for missing dependencies or configuration issues")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
