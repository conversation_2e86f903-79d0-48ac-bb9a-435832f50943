#!/usr/bin/env python3
"""
Test script to demonstrate visible browser scraping with enhanced logging
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('scraping_demo.log')
    ]
)

logger = logging.getLogger(__name__)

async def demo_visible_scraping():
    """Demonstrate visible browser scraping with enhanced logging"""
    
    print("🚀 Facebook Scraping Demo - Visible Browser Mode")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        
        # Initialize scraper
        scraper = FacebookScraper()
        
        # Demo configuration
        demo_config = {
            'profile_id': 1,  # Use profile 1 for demo
            'task_id': 'demo_task_001',
            'post_url': input("📝 Enter Facebook post URL: ").strip(),
            'max_results': int(input("🎯 Enter max comments to scrape (default 20): ") or "20")
        }
        
        print(f"\n📊 Demo Configuration:")
        print(f"   Profile ID: {demo_config['profile_id']}")
        print(f"   Task ID: {demo_config['task_id']}")
        print(f"   Post URL: {demo_config['post_url']}")
        print(f"   Max Results: {demo_config['max_results']}")
        
        print(f"\n🔧 Creating visible browser session...")
        
        # Create scraping session (this will show the browser)
        crawler = await scraper.create_scraping_session(
            demo_config['profile_id'], 
            demo_config['task_id']
        )
        
        if not crawler:
            print("❌ Failed to create scraping session")
            return
        
        print("✅ Browser session created successfully!")
        print("🖥️  You should now see the browser window open")
        
        input("\n⏸️  Press Enter when you're ready to start scraping...")
        
        print("\n🎬 Starting scraping demonstration...")
        print("👀 Watch the browser window to see human-like behaviors:")
        print("   - Natural scrolling patterns")
        print("   - Mouse movements over comments")
        print("   - Reading pauses")
        print("   - Viewport adjustments")
        
        # Start scraping
        results = await scraper.scrape_facebook_post(
            crawler=crawler,
            post_url=demo_config['post_url'],
            scraping_types=[ScrapingType.COMMENTS],
            max_results=demo_config['max_results']
        )
        
        # Display results
        print(f"\n📊 Scraping Results:")
        print(f"   Total Comments: {len(results.get('comments', []))}")
        print(f"   Total Found: {results.get('total_found', 0)}")
        
        if results.get('comments'):
            print(f"\n👥 Sample Comments:")
            for i, comment in enumerate(results['comments'][:5]):
                print(f"   {i+1}. {comment.get('full_name', 'Unknown')} (UID: {comment.get('facebook_uid', 'N/A')})")
                if comment.get('interaction_content'):
                    content = comment['interaction_content'][:100] + "..." if len(comment['interaction_content']) > 100 else comment['interaction_content']
                    print(f"      💬 \"{content}\"")
                print()
        
        if len(results.get('comments', [])) > 5:
            print(f"   ... and {len(results['comments']) - 5} more comments")
        
        print(f"\n📄 Full results saved to scraping_demo.log")
        
        # Keep browser open for inspection
        keep_open = input("\n🔍 Keep browser open for inspection? (y/N): ").lower().strip()
        if keep_open == 'y':
            print("🖥️  Browser will remain open. Close manually when done.")
            input("Press Enter to close the script...")
        
        # Cleanup
        await scraper.cleanup_session(demo_config['task_id'])
        print("✅ Demo completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.exception("Demo error details:")

async def demo_logging_levels():
    """Demonstrate different logging levels"""
    
    print("\n🔧 Logging Level Demo")
    print("-" * 30)
    
    # Set to DEBUG level to see detailed logs
    logging.getLogger('app.services.facebook_scraper').setLevel(logging.DEBUG)
    
    print("📝 Logging levels available:")
    print("   DEBUG: Detailed step-by-step actions")
    print("   INFO: General progress information")
    print("   WARNING: Issues that don't stop execution")
    print("   ERROR: Serious problems")
    
    level = input("\nSelect logging level (DEBUG/INFO/WARNING/ERROR) [INFO]: ").upper().strip()
    if not level:
        level = "INFO"
    
    try:
        log_level = getattr(logging, level)
        logging.getLogger('app.services.facebook_scraper').setLevel(log_level)
        print(f"✅ Logging level set to {level}")
    except AttributeError:
        print("⚠️  Invalid level, using INFO")
        logging.getLogger('app.services.facebook_scraper').setLevel(logging.INFO)

def print_monitoring_tips():
    """Print tips for monitoring the scraping process"""
    
    print("\n👀 Monitoring Tips:")
    print("=" * 40)
    print("🖥️  Browser Window:")
    print("   - Watch for natural scrolling patterns")
    print("   - Notice mouse movements over comments")
    print("   - Observe reading pauses between actions")
    print("   - See viewport adjustments (scroll up/down)")
    
    print("\n📊 Console Logs:")
    print("   - 🔄 Scroll actions with distance and timing")
    print("   - 👤 User data extraction details")
    print("   - 📈 Progress updates and statistics")
    print("   - ⚠️  Warnings for rate limiting or issues")
    
    print("\n🔍 What to Look For:")
    print("   - Smooth, human-like scrolling (not robotic)")
    print("   - Variable timing between actions")
    print("   - Mouse cursor moving naturally")
    print("   - Successful UID extraction from profile links")
    
    print("\n📝 Log File:")
    print("   - Detailed logs saved to 'scraping_demo.log'")
    print("   - Use DEBUG level for maximum detail")

async def main():
    """Main demo function"""
    
    print_monitoring_tips()
    
    # Setup logging level
    await demo_logging_levels()
    
    # Run the demo
    await demo_visible_scraping()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo terminated by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
